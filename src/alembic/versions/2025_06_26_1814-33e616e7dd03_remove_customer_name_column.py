"""remove_customer_name_column

Revision ID: 33e616e7dd03
Revises: 454a930ed676
Create Date: 2025-06-26 18:14:28.102262

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "33e616e7dd03"
down_revision = "454a930ed676"
branch_labels = None
depends_on = None


def upgrade():
    # Step 1: Clear the data in the column
    op.execute("UPDATE orders.customers SET customer_name = NULL")

    op.drop_column("customers", "customer_name", schema="orders")


def downgrade():
    op.add_column(
        "customers", sa.Column("customer_name", sa.String(length=255)), schema="orders"
    )
