"""add_column_ip_and_apn_id_into_sim_card_table

Revision ID: 454a930ed676
Revises: f823e31a46ae
Create Date: 2025-06-19 12:40:34.274640

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "454a930ed676"
down_revision = "f823e31a46ae"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column("sim_card", sa.Column("ip", sa.String(), nullable=True))
    op.add_column("sim_card", sa.Column("apn_id", sa.Integer(), nullable=True))


def downgrade() -> None:
    op.drop_column("sim_card", "ip")
    op.drop_column("sim_card", "apn_id")
