from typing import List, <PERSON><PERSON>
from uuid import UUID

from api.orders.schemas import UpdateOrderStatusRequest, UpdateOrderStatusResponse
from app.config import logger
from auth.dto import AuthenticatedUser
from auth.exceptions import ForbiddenError, Unauthorized
from auth.permissions import is_distributor_admin, is_distributor_client
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from orders.constants import OrderStatus
from orders.domain import model
from orders.services import AbstractOrdersService


class OrdersServiceAuthProxy(AbstractOrdersService):
    def __init__(
        self,
        orders_service: AbstractOrdersService,
        user: AuthenticatedUser,
    ) -> None:
        self.orders_service = orders_service
        self.user = user

    def create_order(
        self, order: model.OrderRequest, user: str | None = None
    ) -> model.OrderResponse:
        if is_distributor_client(self.user):
            # Only check account if organization is a Client
            if (
                self.user.organization.account.id  # type: ignore
                != order.customer_details.customer_account_id  # type: ignore
            ):
                raise ForbiddenError

        return self.orders_service.create_order(order=order, user=self.user.email)

    def update_order_status(
        self,
        order_id: UUID,
        update_data: UpdateOrderStatusRequest,
        user: str | None = None,
    ) -> UpdateOrderStatusResponse:
        if not is_distributor_admin(self.user):
            if (
                update_data.status == OrderStatus.APPROVED.value
                or update_data.status == OrderStatus.SHIPPED.value
            ):
                logger.warning("User is not authorised to change status.")
                raise Unauthorized

        return self.orders_service.update_order_status(
            order_id=order_id, update_data=update_data, user=self.user.email
        )

    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Tuple[List[model.OrdersData], int]:

        return self.orders_service.get_orders(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        return self.orders_service.get_order_details(order_id=order_id)
