import logging
from abc import ABC, abstractmethod
from typing import List
from uuid import UUID

from pydantic import EmailStr
from sqlalchemy.exc import SQLAlchemyError

from api.orders.schemas import UpdateOrderStatusRequest, UpdateOrderStatusResponse
from app.config import logger, settings
from common.ordering import Ordering
from common.pagination import Pagination
from common.parser import ParsingError
from common.searching import Searching
from common.types import FormFactor
from mail_delivery.service import AbstractMailService
from orders.adapters.repository import AbstractOrdersRepository
from orders.constants import OrderStatus, RecipientType
from orders.domain import model
from orders.email_template import OrderData, OrderEmailBuilder
from sim.exceptions import NotFound


class AbstractOrdersService(ABC):
    @abstractmethod
    def create_order(
        self, order: model.OrderRequest, user: str | None = None
    ) -> model.OrderResponse:
        ...

    @abstractmethod
    def update_order_status(
        self,
        order_id: UUID,
        update_data: UpdateOrderStatusRequest,
        user: str | None = None,
    ) -> UpdateOrderStatusResponse:
        ...

    @abstractmethod
    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[List[model.OrdersData], int]:
        ...

    @abstractmethod
    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        ...


class OrdersService(AbstractOrdersService):
    def __init__(
        self,
        orders_repository: AbstractOrdersRepository,
        mail_service: AbstractMailService,
    ):
        self.orders_repository = orders_repository
        self.mail_service = mail_service

    def __constructing_order_data_for_mail(self, order_id: UUID) -> OrderData:
        order_details = self.orders_repository.get_order_details(order_id=order_id)
        sim_type_wise_qty: dict = {
            item.sim_type: item.quantity for item in order_details.order_items
        }
        tracking_info = {}
        if order_details.order_tracking is not None:
            tracking_info = {
                "tracking_url": order_details.order_tracking.reference_url,
                "tracking_ref": order_details.order_tracking.reference_id,
            }
        order_data: OrderData = OrderData(
            order_id=str(order_id),
            order_status=order_details.status,  # type: ignore
            order_date=order_details.order_date.strftime("%Y-%m-%d %H:%M"),
            sim_type_wise_qty=sim_type_wise_qty,
            customer_name=order_details.customer_details.customer_name,
            customer_account_ref=(
                order_details.customer_details.customer_reference  # type: ignore
            ),  # type: ignore
            customer_contact_name=order_details.customer_details.customer_name,
            person_name=(
                order_details.customer_details.person_placing_order  # type: ignore
            ),  # type: ignore
            person_email=order_details.customer_details.customer_email,
            phone_number=order_details.customer_details.customer_contact_no,
            shipping_address_address1=order_details.shipping_details.address_line1,
            shipping_address_address2=order_details.shipping_details.address_line2,
            city=order_details.shipping_details.city,
            state=order_details.shipping_details.state_or_region,
            postal_code=order_details.shipping_details.postal_code,
            country=order_details.shipping_details.country,
            additional_info=order_details.shipping_details.other_information,
            comments=order_details.comments,
            web_link=(
                f"{settings.APP_LANDING_PAGE_URL}"
                f"/sim-management?tab=sim-order&page=1&pageSize=10"
                f"&search={str(order_id)}"
            ),
            **tracking_info,
        )
        return order_data

    def __send_mail(self, order_id: UUID):
        order_data: OrderData = self.__constructing_order_data_for_mail(
            order_id=order_id
        )
        user_email_builder = OrderEmailBuilder(
            order_data=order_data, recipient_type=RecipientType.USER
        )
        manager_email_builder = OrderEmailBuilder(
            order_data=order_data, recipient_type=RecipientType.MANAGER
        )
        try:
            self.mail_service.send_mail(
                subject=user_email_builder.get_subject(),
                name_from="BT IoT Portal",
                recipients=[order_data.person_email],
                html_body=user_email_builder.build_email_body(),
            )
        except Exception as ex:
            logging.error(f"getting Error while sending mail: {ex}")
        if not settings.BT_MANAGER_MAIL_ID:
            raise ValueError(
                "Can not sent mail to BT Manager Please set BT_MANAGER_MAIL_ID"
            )
        try:
            self.mail_service.send_mail(
                subject=manager_email_builder.get_subject(),
                name_from="BT IoT Portal",
                recipients=[
                    EmailStr(email) for email in settings.BT_MANAGER_MAIL_ID.split(";")
                ],
                html_body=manager_email_builder.build_email_body(),
            )
        except Exception as ex:
            logging.error(f"getting Error while sending mail: {ex}")

    def create_order(
        self, order: model.OrderRequest, user: str | None = None
    ) -> model.OrderResponse:
        try:
            for item in order.order_items:
                if item.sim_type in [
                    FormFactor.eSIM_MFF2.value,
                    FormFactor.eSIM_MFF2_eUICC.value,
                ] and (item.quantity is None or item.quantity < 1000):
                    raise ParsingError(
                        (
                            f"Minimum allowed quantity for {item.sim_type} is 1000. "
                            f"Requested: {item.quantity}"
                        )
                    )
            # Adding order and return uuid
            order_response = self.orders_repository.create_order(order=order)
            if not order_response.id:
                logger.error("Failed to create order, no response received.")
                raise ParsingError("Failed to create order, please try again.")

            # Adding order and customer details
            self.orders_repository.add_order_customer(
                customer_details=order.customer_details, order_id=order_response.id
            )
            # Adding order shipping details
            self.orders_repository.add_order_shipping(
                shipping_details=order.shipping_details, order_id=order_response.id
            )

            # Adding order item
            self.orders_repository.add_order_item(
                order_items=order.order_items, order_id=order_response.id
            )

            self.orders_repository.commit_order()
            self.__send_mail(order_id=order_response.id)
            return order_response
        except SQLAlchemyError as e:
            logger.error(f"Error creating order: {e}")
            raise

    def update_order_status(
        self,
        order_id: UUID,
        update_data: UpdateOrderStatusRequest,
        user: str | None = None,
    ) -> UpdateOrderStatusResponse:
        order = self.orders_repository.get_order(order_id)
        # Check if the order exists
        if not order:
            raise NotFound("Data not found.")
        else:
            current_status = order.status
            new_status = update_data.status
        # Define valid status transitions
        allowed_transitions: dict[str, list[str]] = {
            OrderStatus.PENDING.value: [
                OrderStatus.REJECTED.value,
                OrderStatus.APPROVED.value,
            ],
            OrderStatus.APPROVED.value: [
                OrderStatus.REJECTED.value,
                OrderStatus.SHIPPED.value,
            ],
            OrderStatus.SHIPPED.value: [],
        }

        # Validate transition
        if current_status in allowed_transitions:
            if new_status not in allowed_transitions[current_status]:
                raise ParsingError(
                    (
                        f"Invalid status transition from {current_status} "
                        f"to {new_status}"
                    )
                )
        else:
            # If current status is not in allowed transitions
            # disallow changes
            raise ParsingError(
                (
                    f"Cannot update status from {current_status}. "
                    f"Status updates not allowed to {new_status}."
                )
            )

        # Validate tracking info - only allowed for SHIPPED status
        if update_data.status == OrderStatus.SHIPPED.value:
            if not update_data.tracking:
                raise ParsingError(
                    "Tracking information is required for shipped orders"
                )
            if not update_data.tracking.reference_id:
                raise ParsingError(
                    "Tracking reference ID is required for shipped orders"
                )
            if not update_data.tracking.reference_url:
                raise ParsingError(
                    "Tracking reference URL is required for shipped orders"
                )
            self.orders_repository.add_order_tracking(
                order_id=order_id, tracking=update_data.tracking
            )
        elif update_data.tracking:
            raise ParsingError(
                "Tracking information is only allowed for shipped orders"
            )

        # Validate comments - only allowed for REJECTED status
        if update_data.status == OrderStatus.REJECTED.value:
            if not update_data.comments:
                raise ParsingError("Comments are required for rejected orders")
            self.orders_repository.add_reject_reason(
                order_id=order_id, update_data=update_data
            )
        elif update_data.comments:
            raise ParsingError("Comments are only allowed for rejected orders")

        # Update order status
        order_response = self.orders_repository.update_order_status(
            order_id=order_id, update_data=update_data
        )
        self.__send_mail(order_id=order_response.id)
        return order_response

    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[List[model.OrdersData], int]:

        response = self.orders_repository.get_orders(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        count = self.orders_repository.get_orders_count(
            account_id=account_id,
            searching=searching,
        )

        return response, count

    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        order_details = self.orders_repository.get_order_details(order_id=order_id)
        return order_details
