class SimError(Exception):
    ...


class AllocationError(SimError):
    ...


class AllocationDoesNotExist(AllocationError):
    def __init__(self, allocation_id):
        super().__init__(f"Allocation with id:{allocation_id} does not exist.")


class AllocationDeletionNotAllowed(AllocationError):
    def __init__(self, range_id, allocation_id):
        super().__init__(
            f"Allocation with id: {allocation_id} must be the last in "
            f"the range with id: {range_id}."
        )


class InvalidFormFactor(Exception):
    ...


class InsufficientIMSI(AllocationError):
    ...


class RangeError(SimError):
    ...


class RangeDoesNotExist(RangeError):
    def __init__(self, range_id):
        super().__init__(f"Range with id:{range_id} does not exist.")


class RangeNotReady(RangeError):
    ...


class SimCardImsiAlreadyExist(SimError):
    ...


class ImsisAreNotUnique(SimError):
    ...


class EIDsAreNotUnique(SimError):
    ...


class ImsisAreNotContinuity(SimError):
    ...


class SimCardsNotFound(SimError):
    ...


class RatePlanException(Exception):
    ...


class RatePlanNotFound(RatePlanException):
    ...


class RatePlanAccountMappingError(RatePlanException):
    ...


class SimCardsNotOnMAPNetwork(SimError):
    ...


class SimCardsNotOnHLRNetwork(SimError):
    ...


class SimActivityError(SimError):
    ...


class PplAuthError(SimError):
    ...


class SimActivationError(Exception):
    ...


class SimDeActivationError(Exception):
    ...


class IMSIDoesNotExit(SimError):
    def __init__(self, imsi):
        super().__init__(f"IMSI {imsi} does not exist.")


class NoActiveSimFound(SimError):
    def __init__(self, id, month):
        super().__init__(
            f"No active SIMs found for account ID {id} on the specified date {month}."
        )


class NoConnectionHistory(Exception):
    def __init__(self, imsi, month):
        super().__init__(
            f"No history found for IMSI {imsi} on the specified date {month}."
        )


class NoAuditLogs(Exception):
    def __init__(self, imsi, month):
        super().__init__(
            f"No audit logs found for IMSI {imsi} on the specified date {month}."
        )


class NoSimFound(Exception):
    def __init__(self):
        super().__init__("No IMSI found.")


class NoSimCardsStatus(Exception):
    def __init__(self, sim_status):
        super().__init__(f"No SIMs found with {sim_status} status.")


class PplUnknownSubscriber(Exception):
    def __init__(self, imsi):
        super().__init__(f"{imsi} IMSI not allocated to BT.")


class NotAllValuesSameException(Exception):
    ...


class NotMatchPlatformException(Exception):
    ...


class WorkItemIdNotFound(SimError):
    ...


class RangeIntegrityError(Exception):
    ...


class PushNotificationError(Exception):
    ...


class BulkProcessingError(Exception):
    ...


class NotValidFileName(Exception):
    ...


class IMSIAlreadyExist(Exception):
    ...


class SimTypeNotAvailable(Exception):
    ...


class IMSINotAvailableForAllocation(SimError):
    ...


class FileSizeExceededLimit(SimError):
    ...


class IMSIWiseError(SimError):
    ...


class IMSINotAllocated(SimError):
    ...


class IMSINotBelongToSameAccount(Exception):
    ...


class ReAllocationToSameAccount(Exception):
    ...


class IMSIError(SimError):
    ...


class MediaError(Exception):
    ...


class NotFound(SimError):
    ...


class ReAllocationCountError(Exception):
    ...


class MSISDNExitError(Exception):
    ...


class EIDExitError(Exception):
    ...


class SimLimitCountError(Exception):
    ...


class ReAllocationError(Exception):
    ...


class RatePlanChangeNotAllowed(Exception):
    ...


class AlreadyExist(SimError):
    ...


class MSISDNNotFound(Exception):
    ...


class SimAccountProfileError(Exception):
    ...


class UnallocationException(Exception):
    ...


class SIMFlushError(SimError):
    ...


class SIMActiveCallError(SimError):
    ...


class SIMPODError(SimError):
    ...


class SIMSendSMSError(SimError):
    ...


class SimAPNError(SimError):
    ...


class SimALLAPNError(SimError):
    ...


class SIMIPAddressError(SimError):
    ...
