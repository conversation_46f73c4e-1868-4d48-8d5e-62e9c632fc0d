import json
import logging
import re
import uuid
from datetime import datetime
from types import SimpleNamespace

import requests
import xmltodict
from fastapi.encoders import jsonable_encoder
from platform_api_client import PlatformAPIClient, PlatformAPIError
from requests.exceptions import ConnectionError
from starlette import status

from app.config import logger, settings
from audit.exceptions import NoAuditLogs
from auth.exceptions import ForbiddenError, NotFound, Unauthorized
from common.exceptions import AnalyticsAPIError, AuditAPIError, Unprocessable
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import ICCID, IMSI, Month
from sim import exceptions
from sim.domain import model
from sim.domain.model import (
    MSISDN,
    Carrier,
    MarketShareModel,
    MarketShareUsage,
    NotificationStatus,
    RequestType,
    SIMProviderLog,
    SimStatus,
)
from sim.domain.ports import (
    AbstractAuditService,
    AbstractMarketShareAPI,
    AbstractSIMProvisioningAPI,
)


class FakeSIMProvisioningAPI(AbstractSIMProvisioningAPI):
    def __init__(self):
        self.sims_status = {}
        self.active_sims = set()
        self.suspended_sims = set()

    def sim_status(self, imsi: IMSI, msisdn: MSISDN) -> model.SIMStatusResponse:
        # Implementation for fake sim status
        sim_response = model.SIMStatusResponse(
            reference_id=1, imsi=imsi, msisdn=msisdn, sim_status=SimStatus.ACTIVE.name
        )
        return sim_response

    def activate_sim(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        prior_status: SimStatus,
        valid_sim_profile: str,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        # Implementation for fake sim activation
        provider_response = SIMProviderLog(
            sim_activity_log_uuid=str(uuid.uuid4()),
            activity_id=str(uuid.uuid4()),
            audit_date=datetime.now(),
            message="SIM already activated",
            status=SimStatus.ACTIVE.name,
            work_id="64364efa549e801c08d2141a",
            prior_status=prior_status,
        )
        return provider_response

    def suspend_sim(
        self,
        msisdn: MSISDN,
        prior_status: SimStatus,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        # Implementation for fake sim suspension
        provider_response = SIMProviderLog(
            sim_activity_log_uuid=str(uuid.uuid4()),
            activity_id=str(uuid.uuid4()),
            audit_date=datetime.now(),
            message="SIM already deactivated",
            status=SimStatus.DEACTIVATED.name,
            work_id="64364efa549e801c08d2141a",
            prior_status=prior_status,
        )
        return provider_response

    def sim_workitem_status(self, work_id: str) -> model.SIMWorkItemStatusResponse:
        # Implementation for fake sim work item status status
        sim_workitem_response = model.SIMWorkItemStatusResponse(
            reference="1",
            audit_date=datetime.now(),
            request_type=RequestType.CEASE,
            status=NotificationStatus.SUCCESS,
            message="Completed",
        )
        return sim_workitem_response

    def flush_sim(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        client_ip: str | None = None,
    ) -> model.SIMFlushResponse:
        # Implementation for fake sim flush state
        sim_flush_response = model.SIMFlushResponse(
            trid="1",
            message="Completed",
            imsi=IMSI("234588570010011"),
            msisdn=MSISDN("883200000110321"),
        )
        return sim_flush_response

    def pod_sim(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        client_ip: str | None = None,
    ) -> model.SIMPODResponse:
        # Implementation for fake sim POD
        sim_pod_response = model.SIMPODResponse(
            trid="1",
            message="Completed",
            imsi=IMSI("234588570010011"),
            msisdn=MSISDN("883200000110321"),
        )
        return sim_pod_response

    def sms_sim_ppl(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        message: str,
        client_ip: str | None = None,
    ) -> model.SIMSendSMSResponse:
        # Implementation for fake sim SMS
        sim_send_sms_response = model.SIMSendSMSResponse(
            trid="1",
            call_id="13497990410",
            imsi=IMSI("234588570010011"),
            msisdn=MSISDN("883200000110321"),
            message="Completed",
        )
        return sim_send_sms_response

    def get_latest_data_session(
        self,
        iccid: str,
    ) -> model.DataSession:
        # Implementation for fake data session
        data_session = model.DataSession(
            id="254896837",
            partner_id=100,
            iccid=iccid,
            imsi="234588570050716",
            start_time="2025-06-07T10:41:57Z",
            last_updated="2025-06-07T11:26:57Z",
            end_time="2025-06-07T11:26:57Z",
            ip_address="************",
            apn_name="btmeteringandbms",
            bytes_total=0,
            bytes_mo=0,
            bytes_mt=0,
            bytes_limit=0,
            bytes_limit_threshold=0,
            bytes_limit_used=0,
            mobile_country_code="234",
            mobile_network_code="30",
            lac="553D",
            cellId="03D8E12",
        )
        return data_session

    def apn_sim(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        apn_id: str,
        sim_apn_action: model.SIMAPNAction,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        # Implementation for fake sim APN update
        provider_response = SIMProviderLog(
            sim_activity_log_uuid=str(uuid.uuid4()),
            activity_id=str(uuid.uuid4()),
            audit_date=datetime.now(),
            message="SIM already activated",
            status=SimStatus.ACTIVE.name,
            work_id="64364efa549e801c08d2141a",
            prior_status=SimStatus.DEACTIVATED,
        )
        return provider_response


class SimProvisioning(AbstractSIMProvisioningAPI):
    def __init__(self):
        self.data = []

    def _get_customer_status_ppl(self, imsi: str) -> str:
        headers = {"Content-Type": "application/json"}
        request = {
            "UserName": settings.PPL_USERNAME,
            "Password": settings.PPL_PASSWORD,
            "SearchTerm": imsi,
        }
        return json.dumps({"headers": headers, "json": request})

    def _perform_ppl_query(self, imsi: IMSI):
        request_ppl_query = self._get_customer_status_ppl(imsi)
        response_ppl_query_dict = json.loads(request_ppl_query)

        headers = response_ppl_query_dict["headers"]
        json_data = response_ppl_query_dict["json"]

        response_ppl_query = requests.post(
            f"{settings.PPL_URI}{settings.PPL_CUSTOMER_STATUS}",
            headers=headers,
            json=json_data,
            timeout=settings.TIMEOUT,
        )

        return response_ppl_query.json()

    def _handle_ppl_query_failure(self, response_ppl_query, imsi: IMSI, msisdn: MSISDN):
        if response_ppl_query["Message"] == "Not Authenticated":
            logging.error("sim status: PPL auth failed.")
            raise exceptions.PplAuthError("PPL auth failed.")
        elif response_ppl_query["Message"] == "UnknownSubscriber":
            logging.error(f"sim status query IMSI: {imsi} MSIDN: {msisdn}.")
            raise exceptions.PplUnknownSubscriber(imsi)

    def _perform_ppl_hlr_query(self, imsi: IMSI):
        request_ppl_hlrquery = self._get_customer_status_ppl(imsi)
        response_ppl_hlrquery_dict = json.loads(request_ppl_hlrquery)

        headers = response_ppl_hlrquery_dict["headers"]
        json_data = response_ppl_hlrquery_dict["json"]

        response_ppl_hlrquery = requests.post(
            f"{settings.PPL_URI}{settings.PPL_CUSTOMER_STATUS_HLR}",
            headers=headers,
            json=json_data,
            timeout=settings.TIMEOUT,
        )

        return response_ppl_hlrquery.json()

    def _handle_ppl_hlr_query_failure(
        self, response_ppl_hlrquery, imsi: IMSI, msisdn: MSISDN
    ):
        if response_ppl_hlrquery["Message"] == "Not Authenticated":
            logging.error("sim status: PPL auth failed.")
            raise exceptions.PplAuthError("PPL auth failed.")
        elif (
            response_ppl_hlrquery["IMSI"] == 0 and response_ppl_hlrquery["MSISDN"] == 0
        ):
            logging.error(f"sim status hlr query IMSI: {imsi} MSIDN: {msisdn}.")
            raise exceptions.PplUnknownSubscriber(imsi)

    def _get_customer_status_pip(self, msisdn: str) -> str:
        headers = {"Content-Type": "application/xml"}
        xml = (
            """<?xml version='1.0' encoding='utf-8'?>
            <get-customer-subscription version="1">
            <authentication>
            <username>"""
            f"{settings.PIP_USERNAME}"
            """</username>
            <password>"""
            f"{settings.PIP_PASSWORD}"
            """</password>
            </authentication>
            <number>"""
            + msisdn
            + """</number>
            </get-customer-subscription>"""
        )
        return json.dumps({"request": xml, "header": headers})

    def _sim_activate_ppl(self, imsi: str, msisdn: str, valid_sim_profile) -> str:
        headers = {"Content-Type": "application/json"}
        request = {
            "Subscriber": {
                "Client": "BT",
                "Primary": {
                    "Name": "manxm2m",
                    "IMSI": imsi,
                    "MSISDN": msisdn,
                    "HLRProfile": valid_sim_profile,
                },
            },
            "ProvisioningProduct": {"Name": "Create Customer"},
            "UserName": settings.PPL_USERNAME,
            "Password": settings.PPL_PASSWORD,
        }
        return json.dumps({"headers": headers, "json": request})

    def _sim_suspend_ppl(self, msisdn: str) -> str:
        headers = {"Content-Type": "application/json"}
        request = {
            "MSISDN": msisdn,
            "ProvisioningProduct": {"Name": "Close Customer"},
            "UserName": settings.PPL_USERNAME,
            "Password": settings.PPL_PASSWORD,
        }
        return json.dumps({"headers": headers, "json": request})

    def _sim_workitem_ppl(self, work_id: str) -> str:
        headers = {"Content-Type": "application/json"}
        request = {
            "UserName": settings.PPL_USERNAME,
            "Password": settings.PPL_PASSWORD,
            "WorkItemId": work_id,
        }
        return json.dumps({"headers": headers, "json": request})

    def _parse_subscription_response(
        self, xml_response: dict, response_ppl_query: dict, response_ppl_hlrquery: dict
    ) -> model.SIMStatusResponse:
        _reference_id = xml_response["get-customer-subscription-response"]["@trid"]
        _imsi = xml_response["get-customer-subscription-response"]["sims"]["sim"][
            "identities"
        ]["identity"]["profiles"]["profile"]["imsi"]
        _msisdn = xml_response["get-customer-subscription-response"]["sims"]["sim"][
            "identities"
        ]["identity"]["profiles"]["profile"]["msisdn"]
        sim_status = xml_response["get-customer-subscription-response"]["sims"]["sim"][
            "identities"
        ]["identity"]["profiles"]["profile"]["state"]
        _simStatus = sim_status.replace("provisioned", SimStatus.ACTIVE)

        _ppl_query_imsi = response_ppl_query["Subscriber"]["Primary"]["IMSI"]
        _ppl_hlrquery_imsi = response_ppl_hlrquery["IMSI"]

        if _ppl_query_imsi == _ppl_hlrquery_imsi == int(_imsi):
            sim_response = model.SIMStatusResponse(
                reference_id=_reference_id,
                imsi=_imsi,
                msisdn=_msisdn,
                sim_status=_simStatus,
            )
            return sim_response
        else:
            logging.error(f"_pip:{_imsi} _ppl:{_ppl_query_imsi},{_ppl_hlrquery_imsi}")
            raise exceptions.NotAllValuesSameException("IMSI values are not match.")

    def _parse_subscription_error(
        self,
        xml_response: dict,
        response_ppl_query: dict,
        response_ppl_hlrquery: dict,
        imsi: IMSI,
        msisdn: MSISDN,
    ) -> model.SIMStatusResponse:
        _reference_id = xml_response["get-customer-subscription-error"]["@trid"]
        _imsi = imsi
        _msisdn = msisdn
        _simStatus = SimStatus.DEACTIVATED
        _ppl_query_msisdn = response_ppl_query["Subscriber"]["Primary"]
        _ppl_hlrquery_msisdn = response_ppl_hlrquery["MSISDN"]

        if "MSISDN" not in _ppl_query_msisdn and _ppl_hlrquery_msisdn == 0:
            sim_response = model.SIMStatusResponse(
                reference_id=_reference_id,
                imsi=_imsi,
                msisdn=_msisdn,
                sim_status=_simStatus,
            )
            return sim_response
        else:
            logging.error(
                f"_pip:{msisdn} _ppl:{_ppl_query_msisdn},{_ppl_hlrquery_msisdn}"
            )
            raise exceptions.NotMatchPlatformException(
                "MSISDN value not match on platform."
            )

    def _parse_xml_response(
        self, xml_response: dict, imsi: IMSI, msisdn: MSISDN
    ) -> model.SIMStatusResponse:
        response_ppl_query = self._perform_ppl_query(imsi)
        if not response_ppl_query["IsSuccess"]:
            self._handle_ppl_query_failure(response_ppl_query, imsi, msisdn)

        response_ppl_hlrquery = self._perform_ppl_hlr_query(imsi)
        if not response_ppl_hlrquery["IsSuccess"]:
            self._handle_ppl_hlr_query_failure(response_ppl_hlrquery, imsi, msisdn)

        if "get-customer-subscription-response" in xml_response:
            return self._parse_subscription_response(
                xml_response, response_ppl_query, response_ppl_hlrquery
            )
        else:
            return self._parse_subscription_error(
                xml_response, response_ppl_query, response_ppl_hlrquery, imsi, msisdn
            )

    def sim_status(self, imsi: IMSI, msisdn: MSISDN) -> model.SIMStatusResponse:
        request = self._get_customer_status_pip(msisdn)
        response_dict = json.loads(request)

        headers = response_dict["header"]
        xml_data = response_dict["request"]

        response = requests.post(
            str(settings.PIP_URI),
            xml_data,
            headers,
            timeout=settings.TIMEOUT,
        )

        xml_response = xmltodict.parse(response.text)
        sim_response = self._parse_xml_response(xml_response, imsi, msisdn)

        return sim_response

    def activate_sim(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        prior_status: SimStatus,
        valid_sim_profile: str,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        request = self._sim_activate_ppl(imsi, msisdn, valid_sim_profile)

        response_dict = json.loads(request)

        headers = response_dict["headers"]
        json_data = response_dict["json"]

        response = requests.post(
            f"{settings.PPL_URI}{settings.PPL_SIM_ACTIVATE}",
            headers=headers,
            json=json_data,
            timeout=settings.TIMEOUT,
        )

        provision_data = response.json()

        if provision_data["Status"] == "NOTAUTHENTICATED":
            logging.error("activate_sim: PPL auth failed.")
            raise exceptions.PplAuthError("PPL auth failed.")

        logging.info("activate_sim work_item_id: " + provision_data["WorkItemId"])

        provider_response = SIMProviderLog(
            sim_activity_log_uuid=str(uuid.uuid4()),
            activity_id=str(uuid.uuid4()),
            audit_date=provision_data["AuditDate"],
            message=(provision_data["Message"]).title(),
            status=(provision_data["Status"]).title(),
            work_id=provision_data["WorkItemId"],
            prior_status=prior_status,
        )

        return provider_response

    def suspend_sim(
        self, msisdn: MSISDN, prior_status: SimStatus, client_ip: str | None = None
    ) -> SIMProviderLog:
        request = self._sim_suspend_ppl(msisdn)
        response_dict = json.loads(request)

        headers = response_dict["headers"]
        json_data = response_dict["json"]

        response = requests.post(
            f"{settings.PPL_URI}{settings.PPL_SIM_SUSPEND}",
            headers=headers,
            json=json_data,
            timeout=settings.TIMEOUT,
        )

        suspend_data = response.json()

        if suspend_data["Status"] == "NOTAUTHENTICATED":
            logging.error("suspend_sim: PPL auth failed.")
            raise exceptions.PplAuthError("PPL auth failed.")

        logging.info("suspend_sim work_item_id: " + suspend_data["WorkItemId"])
        provider_response = SIMProviderLog(
            sim_activity_log_uuid=str(uuid.uuid4()),
            activity_id=str(uuid.uuid4()),
            audit_date=suspend_data["AuditDate"],
            message=(suspend_data["Message"]).title(),
            status=(suspend_data["Status"]).title(),
            work_id=suspend_data["WorkItemId"],
            prior_status=prior_status,
        )
        return provider_response

    def sim_workitem_status(self, work_id: str) -> model.SIMWorkItemStatusResponse:
        request = self._sim_workitem_ppl(work_id)
        response_dict = json.loads(request)

        headers = response_dict["headers"]
        json_data = response_dict["json"]

        response = requests.post(
            f"{settings.PPL_URI}{settings.PPL_SIM_WORKSTATUS}",
            headers=headers,
            json=json_data,
            timeout=settings.TIMEOUT,
        )

        workstatus_data = response.json()
        if workstatus_data["Status"] == "NOTAUTHENTICATED":
            logging.error("PPL auth failed.")
            raise exceptions.PplAuthError("PPL auth failed.")

        logging.info("work_item_id: " + workstatus_data["WorkItemId"])
        workstatus_response = model.SIMWorkItemStatusResponse(
            reference=workstatus_data["Reference"],
            audit_date=workstatus_data["AuditDate"],
            request_type=workstatus_data["RequestType"].capitalize(),
            status=workstatus_data["Status"],
            message=workstatus_data["Message"],
        )
        return workstatus_response

    def _flush_sim_state_pip(self, msisdn: str) -> str:
        headers = {"Content-Type": "application/xml"}
        # <number>883200000110322</number> For Testing Purpose
        xml = (
            """<?xml version='1.0' encoding='utf-8'?>
            <flush-sim-state version="1">
            <authentication>
            <username>"""
            f"{settings.PIP_USERNAME}"
            """</username>
            <password>"""
            f"{settings.PIP_PASSWORD}"
            """</password>
            </authentication>
            <number>"""
            + msisdn
            + """</number>
            </flush-sim-state>"""
        )
        return json.dumps({"request": xml, "header": headers})

    def _parse_flush_sim_response(
        self, xml_response: dict, imsi: IMSI, msisdn: MSISDN
    ) -> model.SIMFlushResponse:
        tag, content = next(iter(xml_response.items()))

        trid = content.get("@trid", "")
        message = content.get("#text", "").strip() or (
            "success" if "response" in tag else "error"
        )
        if tag.lower() == "flush-sim-state-error":
            raise exceptions.SIMFlushError(f"{message}")

        return model.SIMFlushResponse(
            trid=trid, message=message, imsi=imsi, msisdn=msisdn
        )

    def flush_sim(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        client_ip: str | None = None,
    ) -> model.SIMFlushResponse:
        request = self._flush_sim_state_pip(msisdn)
        response_dict = json.loads(request)

        headers = response_dict["header"]
        xml_data = response_dict["request"]

        response = requests.post(
            str(settings.PIP_URI),
            xml_data,
            headers,
            timeout=settings.TIMEOUT,
        )

        xml_response = xmltodict.parse(response.text)
        sim_response = self._parse_flush_sim_response(xml_response, imsi, msisdn)

        return sim_response

    def _pod_sim_pip(self, msisdn: str, call_id: str) -> str:
        headers = {"Content-Type": "application/xml"}
        # <call-id>"""    Testing Purpose
        # + 618738221
        # + """</call-id>
        xml = (
            """<?xml version='1.0' encoding='utf-8'?>
            <disconnect-data-session version="1">
            <authentication>
            <username>"""
            f"{settings.PIP_USERNAME}"
            """</username>
            <password>"""
            f"{settings.PIP_PASSWORD}"
            """</password>
            </authentication>
            <number>"""
            + msisdn
            + """</number>
            <call-id>"""
            + call_id
            + """</call-id>
            </disconnect-data-session>"""
        )
        return json.dumps({"request": xml, "header": headers})

    def _parse_pod_sim_response(
        self, xml_response: dict, imsi: IMSI, msisdn: MSISDN
    ) -> model.SIMPODResponse:
        tag, content = next(iter(xml_response.items()))

        trid = content.get("@trid", "")
        message = content.get("#text", "").strip() or (
            "success" if "response" in tag else "error"
        )

        if tag.lower() == "disconnect-data-session-error":
            raise exceptions.SIMPODError(f"{message}")

        return model.SIMPODResponse(
            trid=trid, message=message, imsi=imsi, msisdn=msisdn
        )

    def pod_sim(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        client_ip: str | None = None,
    ) -> model.SIMPODResponse:
        query_response = self.query_sim_active_call(imsi=imsi, msisdn=msisdn)
        request = self._pod_sim_pip(msisdn, query_response.call_id)
        response_dict = json.loads(request)

        headers = response_dict["header"]
        xml_data = response_dict["request"]

        response = requests.post(
            str(settings.PIP_URI),
            xml_data,
            headers,
            timeout=settings.TIMEOUT,
        )

        xml_response = xmltodict.parse(response.text)
        sim_response = self._parse_pod_sim_response(xml_response, imsi, msisdn)

        return sim_response

    def _query_sim_active_call_pip(self, msisdn: str) -> str:
        headers = {"Content-Type": "application/xml"}
        # <number>883200000110322</number> For Testing Purpose
        xml = (
            """<?xml version='1.0' encoding='utf-8'?>
            <query-active-call version="1">
            <authentication>
            <username>"""
            f"{settings.PIP_USERNAME}"
            """</username>
            <password>"""
            f"{settings.PIP_PASSWORD}"
            """</password>
            </authentication>
            <number>"""
            + msisdn
            + """</number>
            <call-type>gsm-data</call-type>
            </query-active-call>"""
        )
        return json.dumps({"request": xml, "header": headers})

    def _parse_query_sim_active_call_response(
        self, xml_response: dict, imsi: IMSI, msisdn: MSISDN
    ) -> model.QuerySIMActiveCallResponse:
        tag, content = next(iter(xml_response.items()))

        trid = content.get("@trid", "")
        call_id = content.get("call-id", "")
        message = content.get("#text", "").strip() or (
            "success" if "response" in tag else "error"
        )

        if tag.lower() == "query-active-call-error":
            raise exceptions.SIMActiveCallError(f"{message}")

        return model.QuerySIMActiveCallResponse(
            trid=trid, call_id=call_id, message=message, imsi=imsi, msisdn=msisdn
        )

    def query_sim_active_call(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        client_ip: str | None = None,
    ) -> model.QuerySIMActiveCallResponse:
        request = self._query_sim_active_call_pip(msisdn)
        response_dict = json.loads(request)

        headers = response_dict["header"]
        xml_data = response_dict["request"]

        response = requests.post(
            str(settings.PIP_URI),
            xml_data,
            headers,
            timeout=settings.TIMEOUT,
        )

        xml_response = xmltodict.parse(response.text)
        sim_response = self._parse_query_sim_active_call_response(
            xml_response, imsi, msisdn
        )

        return sim_response

    def _send_sim_sms_pip(self, msisdn: str, message: str) -> str:
        headers = {"Content-Type": "application/xml"}
        # <number>883200000110322</number> Testing Purpose
        # <origin>447624123456</origin>
        xml = (
            """<?xml version='1.0' encoding='utf-8'?>
            <send-sms version="1">
            <authentication>
            <username>"""
            f"{settings.PIP_USERNAME}"
            """</username>
            <password>"""
            f"{settings.PIP_PASSWORD}"
            """</password>
            </authentication>
            <number>"""
            + msisdn
            + """</number>
            <origin>"""
            + str(settings.PIP_SMS_ORIGIN or "")
            + """</origin>
            <message-text>"""
            + message
            + """</message-text>
            <data-coding-scheme>"""
            + str(settings.PIP_SMS_LENGTH or "")
            + """</data-coding-scheme>
            </send-sms>"""
        )
        return json.dumps({"request": xml, "header": headers})

    def _parse_send_sim_sms_response(
        self, xml_response: dict, imsi: IMSI, msisdn: MSISDN
    ) -> model.SIMSendSMSResponse:
        tag, content = next(iter(xml_response.items()))

        trid = content.get("@trid", "")
        call_id = content.get("call-id", "")
        message = content.get("#text", "").strip() or (
            "success" if "response" in tag else "error"
        )

        if tag.lower() == "send-sms-error":
            raise exceptions.SIMSendSMSError(f"{message}")

        return model.SIMSendSMSResponse(
            trid=trid,
            call_id=call_id,
            imsi=imsi,
            msisdn=MSISDN(msisdn),
            message=message,
        )

    def sms_sim_ppl(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        message: str,
        client_ip: str | None = None,
    ) -> model.SIMSendSMSResponse:
        request = self._send_sim_sms_pip(msisdn, message)
        response_dict = json.loads(request)

        headers = response_dict["header"]
        xml_data = response_dict["request"]

        response = requests.post(
            str(settings.PIP_URI),
            xml_data,
            headers,
            timeout=settings.TIMEOUT,
        )

        xml_response = xmltodict.parse(response.text)
        sim_response = self._parse_send_sim_sms_response(xml_response, imsi, msisdn)

        return sim_response

    def _sim_apn_ppl(
        self, msisdn: str, apn_id: str, sim_apn_action: model.SIMAPNAction
    ) -> str:
        headers = {"Content-Type": "application/json"}
        request = {
            "MSISDN": msisdn,
            "APNID": apn_id,
            "Action": sim_apn_action,
            "UserName": settings.PPL_USERNAME,
            "Password": settings.PPL_PASSWORD,
        }
        return json.dumps({"headers": headers, "json": request})

    def apn_sim(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        apn_id: str,
        sim_apn_action: model.SIMAPNAction,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        request = self._sim_apn_ppl(msisdn, apn_id, sim_apn_action)

        response_dict = json.loads(request)

        headers = response_dict["headers"]
        json_data = response_dict["json"]

        response = requests.post(
            f"{settings.PPL_URI}{settings.PPL_SIM_UPDATE_APN}",
            headers=headers,
            json=json_data,
            timeout=settings.TIMEOUT,
        )

        update_apn_data = response.json()

        if update_apn_data["Status"] == "NOTAUTHENTICATED":
            logging.error("update_apn_sim: PPL auth failed.")
            raise exceptions.PplAuthError("PPL auth failed.")

        logging.info("update_apn_sim work_item_id: " + update_apn_data["WorkItemId"])

        provider_response = SIMProviderLog(
            sim_activity_log_uuid=str(uuid.uuid4()),
            activity_id=str(uuid.uuid4()),
            audit_date=update_apn_data["AuditDate"],
            message=(update_apn_data["Message"]).title(),
            status=(update_apn_data["Status"]).title(),
            work_id=update_apn_data["WorkItemId"],
            prior_status=None,  # type: ignore
        )

        return provider_response

    def _get_worldov_access_token(self) -> str:
        """
        This is a temporary function to get access token from WorldOV API dynamically.
        This function will be removed once we have a proper
        authentication flow in place.
        """
        auth_url = "https://api.worldov.net/v1/auth/login"

        auth_payload = {
            "username": "<EMAIL>",
            "password": "OV1Pa55word!",
            "application_token": "bd60582b-ba09-4f90-bb4d-ccbfbc0ef162",
        }

        headers = {"accept": "application/json", "content-type": "application/*+json"}

        try:
            response = requests.post(
                auth_url,
                headers=headers,
                json=auth_payload,
                timeout=settings.TIMEOUT,
            )

            if response.status_code == 200:
                auth_data = response.json()
                access_token = auth_data.get("authentication_token")
                if access_token:
                    logger.info("Successfully obtained WorldOV access token")
                    return access_token
                else:
                    logger.error("No authentication_token in response")
                    raise exceptions.SimError("No authentication token in response")
            else:
                logger.error(
                    f"Failed to authenticate with WorldOV: {response.status_code}"
                )
                raise exceptions.SimError(
                    f"Authentication failed: {response.status_code}"
                )

        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed for WorldOV authentication: {e}")
            raise exceptions.SimError(f"Authentication request failed: {e}")

    def get_latest_data_session(
        self,
        iccid: str,
    ) -> model.DataSession:
        """Get latest data session for a SIM by ICCID from WorldOV API"""
        # Get access token dynamically
        access_token = self._get_worldov_access_token()
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        url = f"{settings.WORLDOV_API_BASE_URL}/data_sessions/{iccid}/latest"

        try:
            response = requests.get(
                url,
                headers=headers,
                timeout=settings.TIMEOUT,
            )

            if response.status_code == 200:
                data = response.json()
                return model.DataSession(
                    id=data["id"],
                    partner_id=data["partner_id"],
                    iccid=data["iccid"],
                    imsi=data["imsi"],
                    start_time=data["start_time"],
                    last_updated=data["last_updated"],
                    end_time=data["end_time"],
                    ip_address=data["ip_address"],
                    apn_name=data["apn_name"],
                    bytes_total=data["bytes_total"],
                    bytes_mo=data["bytes_mo"],
                    bytes_mt=data["bytes_mt"],
                    bytes_limit=data["bytes_limit"],
                    bytes_limit_threshold=data["bytes_limit_threshold"],
                    bytes_limit_used=data["bytes_limit_used"],
                    mobile_country_code=data["mobile_country_code"],
                    mobile_network_code=data["mobile_network_code"],
                    lac=data["lac"],
                    cellId=data["cellid"],
                )
            else:
                logger.error(
                    f"Failed to get data session for ICCID {iccid}: "
                    f"{response.status_code}"
                )
                raise exceptions.SimError(
                    f"Failed to get data session: {response.status_code}"
                )

        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed for data session ICCID {iccid}: {e}")
            raise exceptions.SimError(f"Request failed: {e}")


class FakeMarketShareAPI(AbstractMarketShareAPI):
    def __init__(self):
        self.get_imsis_usages = {}

    def market_share_usage(
        self, request_data: MarketShareModel
    ) -> model.MarketShareCarrier:
        carrier_object = Carrier("GBRME")
        carrier_usage_list = [
            MarketShareUsage(carrier=carrier_object, usage=5897),
            MarketShareUsage(carrier=carrier_object, usage=1234),
        ]
        return model.MarketShareCarrier(totalUsage=94311, summary=carrier_usage_list)

    def get_imsis_usage(
        self, request_data: model.MarketShareModel
    ) -> model.MarketShare:
        totalUsage: int = 56561
        summary: list = [
            {
                "imsi": "***************",
                "usageSummary": [{"carrier": "GBRMO", "usage": 8556}],
            },
            {
                "imsi": "***************",
                "usageSummary": [{"carrier": "GBRMO", "usage": 8556}],
            },
            {
                "imsi": "234588570010009",
                "usageSummary": [{"carrier": "GBRME", "usage": 4125}],
            },
            {
                "imsi": "234588570010009",
                "usageSummary": [{"carrier": "GBRMT", "usage": 633}],
            },
        ]
        _result: dict = {"totalUsage": totalUsage, "summary": summary}
        if _result["summary"] is None:
            raise Exception("Summary is Empty")
        market_share_usage = model.MarketShare(
            total_usage=_result["totalUsage"], summary=_result["summary"]
        )
        logger.info(f"TEST response {market_share_usage}")
        return market_share_usage


class HTTPMarketShareAPI(AbstractMarketShareAPI):
    def __init__(self, api_client: PlatformAPIClient):
        self.api = api_client

    def _market_share_request_data(self, request_data: MarketShareModel) -> str:
        from_date = (
            request_data.from_date.isoformat() if request_data.from_date else None
        )
        to_date = request_data.to_date.isoformat() if request_data.to_date else None
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json",
        }
        request = {
            "fromDate": from_date,
            "toDate": to_date,
            "imsis": request_data.imsis,
        }
        return json.dumps({"headers": headers, "json": request})

    def market_share_usage(
        self, request_data: MarketShareModel
    ) -> model.MarketShareCarrier:
        try:
            request = self._market_share_request_data(request_data)
            response_dict = json.loads(request)
            headers = response_dict["headers"]
            json_data = response_dict["json"]
            marketShareURL = f"{settings.APP_BASE_URL}{settings.MARKET_SHARE_SUMMARY}"
            logger.info(f"marketShareURL :{marketShareURL} json_data: {json_data}")
            response = self.api.post(
                url=marketShareURL, headers=headers, json=json_data
            )
            if response.status_code == 200:
                _result = response.json()
                if not _result["summary"]:
                    raise NotFound("Response is received but no summary data found")
                market_share_usage = model.MarketShareCarrier(
                    totalUsage=_result["totalUsage"], summary=_result["summary"]
                )
                return market_share_usage
            elif response.status_code == 422:
                raise Unprocessable("Unprocessable")
            elif response.status_code == 401:
                raise Unauthorized("Unauthorized")
            elif response.status_code == 404:
                raise NotFound("NotFound")
            else:
                raise AnalyticsAPIError
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound("NotFound")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError as e:
            raise ConnectionError(f"Failed to connect Market Share API {e}")

    def _get_imsis_usage(self, request_data: model.MarketShareModel):
        from_date = (
            request_data.from_date.isoformat() if request_data.from_date else None
        )
        to_date = request_data.to_date.isoformat() if request_data.to_date else None
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json",
        }
        json_data = {
            "fromDate": from_date,
            "toDate": to_date,
            "imsis": request_data.imsis,
        }

        return json.dumps({"headers": headers, "json": json_data})

    def get_imsis_usage(
        self, request_data: model.MarketShareModel
    ) -> model.MarketShare:

        try:
            request = self._get_imsis_usage(request_data)
            response_dict = json.loads(request)
            headers = response_dict["headers"]
            json_data = response_dict["json"]
            marketShareURL = (
                f"{settings.APP_BASE_URL}{settings.IMSI_USAGE_ANALYTICS_API}"
            )
            response = self.api.post(
                url=marketShareURL, headers=headers, json=json_data
            )
            logger.info(
                f" 'marketShareURL:- {marketShareURL}', \
                records:- {response.json()}, status_code:- {response.status_code}"
            )

            if response.status_code == 200:
                _result = response.json()
                if _result["summary"] is None:
                    raise Exception(
                        "Response is received but no Summary data \
                        found for the requested imsi"
                    )
                market_share_usage = model.MarketShare(
                    total_usage=_result["totalUsage"], summary=_result["summary"]
                )
                return market_share_usage

            if response.status_code == 401:
                logger.info("Authentication issue")
                raise Unauthorized()
            if response.status_code == 404:
                logger.info("Data_not found from the marketshare api call.")
            market_share_usage = model.MarketShare(total_usage=0, summary=[])
            return market_share_usage
        except PlatformAPIError:
            return model.MarketShare(total_usage=0, summary=[])
        except ConnectionError as e:
            raise ConnectionError(f"Failed to connect Market Share API {e}")


class AuditServiceAPI(AbstractAuditService):
    REALLOCATION_URL = "/v1/auditlog/reallocation"
    ALLOCATION_URL = "/v1/auditlog/allocation"
    SIM_AUDIT_URL = "/v1/auditlog/sim"
    SIM_PROVIDER_AUDIT_URL = "/v1/auditlog/sim/provider"
    SIM_PENDING_INFO_URL = "/v1/auditlog/sim/{imsi}/pending"
    SIM_WORKID_INFO_URL = "/v1/auditlog/sim/provider/{work_id}"
    SIM_MSISDN_UPDATE_COUNT_URL = "/v1/auditlog/sim/details/count"
    GET_SIM_AUDIT = (
        "/v1/auditlog/sim/{month}?is_client={is_client}"
        + "&page={page}&page_size={page_size}&ordering={ordering}"
    )
    GET_ACCOUNT_AUDIT = (
        "/v1/auditlog/account?is_client={is_client}"
        + "&page={page}&page_size={page_size}&ordering={ordering}"
    )
    VALIDATE_NOTIFICATION_URL = "/v1/auditlog/sim/provider/{sim_status}"
    SIM_MSISDN_POOL_URL = "/v1/auditlog/msisdn/pool"
    ACCOUNT_ACTIVITY_URL = "/v1/auditlog/account_log"
    UPLOADED_FILE_STATUS_URL = "/v1/auditlog/request"
    SIM_ACTION_AUDIT_URL = "/v1/auditlog/sim_action"

    def __init__(self, api_client: PlatformAPIClient):
        self.api = api_client

    def add_reallocation_audit_api(self, audit_details: list[model.SIMCardAudit]):
        try:
            logging.debug(f"Re_allocation_audit_details: {audit_details}")
            headers = {"Content-Type": "application/json"}
            url = f"{settings.APP_BASE_URL}{self.REALLOCATION_URL}"
            logging.debug(f"Re_allocation_url: {url}")
            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder(audit_details),
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error("Error in add_reallocation_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def add_allocation_audit_api(self, audit_details: list[model.SIMCardAudit]):
        try:
            logging.debug(f"Allocation_audit_details: {audit_details}")
            headers = {"Content-Type": "application/json"}
            url = f"{settings.APP_BASE_URL}{self.ALLOCATION_URL}"
            logging.debug(f"Allocation_url: {url}")
            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder(audit_details),
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error("Error in add_allocation_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def add_sim_audit_api(self, audit_details: list[model.SIMCardAudit]):
        try:
            logging.debug(f"SIM_audit_details: {audit_details}")
            headers = {"Content-Type": "application/json"}
            url = f"{settings.APP_BASE_URL}{self.SIM_AUDIT_URL}"
            logging.debug(f"SIM_AUDIT_URL: {url}")
            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder(audit_details),
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error("Error in add_sim_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def add_sim_provider_audit_api(self, audit_details: model.SIMCardProviderAudit):
        try:
            logging.debug(f"SIMProvider_audit_details: {audit_details}")
            headers = {"Content-Type": "application/json"}
            url = f"{settings.APP_BASE_URL}{self.SIM_PROVIDER_AUDIT_URL}"
            logging.debug(f"SIM_PROVIDER_AUDIT_URL: {url}")

            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder(audit_details),
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error("Error in add_sim_provider_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    logger.error("add_sim_provider_audit_api Authentication issue")
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def get_sim_pending_info_audit_api(self, imsi: IMSI):
        url = self.SIM_PENDING_INFO_URL.format(imsi=str(imsi))
        try:
            response = self.api.get(url)
            # Parse the response JSON
            response_json = response.json()
            work_id = response_json.get("workId")  # Adjust key to match actual response
        except PlatformAPIError as e:
            logger.error("Error in get_sim_pending_info_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_404_NOT_FOUND:
                    return None
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        else:
            return work_id

    def process_log(self, response_json):
        return SimpleNamespace(**response_json)

    def get_sim_provider_log_audit_api(self, work_id: str):
        url = self.SIM_WORKID_INFO_URL.format(work_id=work_id)
        try:
            response = self.api.get(url)
            response_json = response.json()
            provider_log = self.process_log(response_json)
        except PlatformAPIError as e:
            logger.error("Error in get_sim_provider_log_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    logger.error("get_sim_provider_log_audit_api Authentication issue")
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_404_NOT_FOUND:
                    return None
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_502_BAD_GATEWAY:
                    raise Exception()
                case _:
                    raise e
        else:
            return provider_log

    def get_sim_audit_api(
        self,
        imsi: IMSI,
        month: Month,
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        try:
            logging.debug(
                f"SIM Auidt for : {imsi}{is_client}\
                    {month}{pagination}{ordering}{searching}"
            )

            if ordering:
                order_prefix = "-" if ordering.order.value == "DESC" else ""
                ordering_param = f"{order_prefix}{self._camel_to_snake(ordering.field)}"

            _url = self.GET_SIM_AUDIT.format(
                month=month,
                is_client=is_client,
                page=pagination.page,  # type: ignore
                page_size=pagination.page_size,  # type: ignore
                ordering=ordering_param,
            )
            if searching:
                _url += f"&search={searching.search}"

            headers = {"Content-Type": "application/json"}
            url = f"{settings.APP_BASE_URL}{_url}"
            logging.debug(f"get_sim_audit_url: {url}")
            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder([imsi]),
            )
            if response.status_code == 200:
                auudit_log = self.process_log(response.json())
                return auudit_log
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error(f"Error {e} .:")
            logger.error("Error in get_sim_audit_api .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_404_NOT_FOUND:
                    raise exceptions.NoAuditLogs(imsi=imsi, month=month)
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def _camel_to_snake(self, name: str) -> str:
        """
        Convert camelCase to snake_case.
        """
        s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
        return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()

    def get_account_audit_api(
        self,
        imsi: list[IMSI],
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        try:
            logging.debug(
                f"SIM Auidt for : {imsi}{is_client}\
                    {pagination}{ordering}{searching}"
            )
            if ordering:
                order_prefix = "-" if ordering.order.value == "DESC" else ""
                ordering_param = f"{order_prefix}{self._camel_to_snake(ordering.field)}"

            _url = self.GET_ACCOUNT_AUDIT.format(
                is_client=is_client,
                page=pagination.page,  # type: ignore
                page_size=pagination.page_size,  # type: ignore
                ordering=ordering_param,
            )
            if searching:
                _url += f"&search={searching.search}"

            headers = {"Content-Type": "application/json"}
            url = f"{settings.APP_BASE_URL}{_url}"
            logging.debug(f"get_sim_audit_url: {url}")
            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder(imsi),
            )
            if response.status_code == 200:
                auudit_log = self.process_log(response.json())
                return auudit_log
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error("Error in get_account_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_404_NOT_FOUND:
                    raise NoAuditLogs("No audit logs found for the requested imsis.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def validate_notification(
        self, sim_provider_log: model.SIMProviderLog, sim_status: str
    ) -> bool:
        url = self.VALIDATE_NOTIFICATION_URL.format(sim_status=sim_status)
        headers = {"Content-Type": "application/json"}
        logging.debug(f"process notification: {sim_status}")
        try:
            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder(sim_provider_log),
            )
            logging.debug(f"validate_notification response: {response}")
            response_json = response.json()
        except PlatformAPIError as e:
            logger.error("Error in validate_notification .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    logger.error("validate_notification Authentication issue")
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_404_NOT_FOUND:
                    raise AuditAPIError()
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        else:
            return response_json

    def add_upload_msisdn_audit_api(
        self, audit_details: list[model.MsisdnPoolAudit] | list[model.SIMCardAudit]
    ):
        try:
            logger.info(f"SIM_audit_details: {audit_details}")
            headers = {"Content-Type": "application/json"}
            url = f"{settings.APP_BASE_URL}{self.SIM_MSISDN_POOL_URL}"
            logger.info(f"SIM_MSISDN_POOL_URL: {url}")
            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder(audit_details),
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error("Error in add_upload_msisdn_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def get_sim_msisdn_update_log_audit_api(self):
        """
        This gives last 5 days of update msisdn count
        """
        url = self.SIM_MSISDN_UPDATE_COUNT_URL
        try:
            response = self.api.get(url)

            if response.status_code == 200:
                update_msisdn_log = response.json()
                return update_msisdn_log["update_sim_msisdn_count"]
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error("Error in get_sim_msisdn_update_log_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound(f"{e.message}")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_502_BAD_GATEWAY:
                    raise Exception()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def add_account_audit_log(
        self, audit_detail: model.AccountActivityLog
    ) -> model.AccountAuditReponse:
        try:
            logger.info(f"Account Activity Log: {audit_detail}")
            url = f"{settings.APP_BASE_URL}{self.ACCOUNT_ACTIVITY_URL}"
            logger.info(f"ACCOUNT_ACTIVITY_LOG_URL: {url}")
            response = self.api.post(
                url=url,
                json=jsonable_encoder(audit_detail),
            )
            if response.status_code == 201:
                return model.AccountAuditReponse(response.json()["id"])
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error("Error in add_account_audit_log .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound(f"{e.message}")
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def add_uploaded_file_status_audit_api(self, file_details: model.UploadedFileAudit):
        try:
            headers = {"Content-Type": "application/json"}
            url = f"{settings.APP_BASE_URL}{self.UPLOADED_FILE_STATUS_URL}"
            logger.info(f"Uploaded file status url: {url}")
            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder(file_details),
            )
            if response.status_code == 201:
                logger.info(f"Uploaded file status response: {response.json()}")
                return True
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error("Error in add_uploaded_file_status_audit_api .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def add_sim_action_audit_api(self, sim_action_audit_details: model.SIMActionAudit):
        try:
            logging.debug(f"SIM_Action_audit_details: {sim_action_audit_details}")
            headers = {"Content-Type": "application/json"}
            url = f"{settings.APP_BASE_URL}{self.SIM_ACTION_AUDIT_URL}"
            logging.debug(f"SIM_AUDIT_URL: {url}")
            response = self.api.post(
                url=url,
                headers=headers,
                json=jsonable_encoder(sim_action_audit_details),
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in add_sim_action_audit_api .: {e}")


class FakeAuditServiceAPI(AbstractAuditService):
    REALLOCATION_URL = "/v1/auditlog/reallocation"
    ALLOCATION_URL = "/v1/auditlog/allocation"
    SIM_AUDIT_URL = "/v1/auditlog/sim"
    SIM_PROVIDER_AUDIT_URL = "/v1/auditlog/sim/provider"
    SIM_PENDING_INFO_URL = "/v1/auditlog/sim/{imsi}/pending"
    SIM_WORKID_INFO_URL = "/v1/auditlog/sim/provider/{work_id}"
    GET_SIM_AUDIT = (
        "/v1/auditlog/sim/{month}?is_client={is_client}"
        + "&page={page}&page_size={page_size}&ordering={ordering}"
    )
    GET_ACCOUNT_AUDIT = (
        "/v1/auditlog/account?is_client={is_client}"
        + "&page={page}&page_size={page_size}&ordering={ordering}"
    )
    SIM_MSISDN_POOL_URL = "/v1/auditlog/msisdn/pool"
    ACCOUNT_ACTIVITY_URL = "/v1/auditlog/account_log"
    UPLOADED_FILE_STATUS_URL = "/v1/auditlog/request"
    SIM_ACTION_AUDIT_URL = "/v1/auditlog/sim_action"

    def add_reallocation_audit_api(self, audit_details: list[model.SIMCardAudit]):
        audit_details = [
            model.SIMCardAudit(
                imsi="***************",  # type: ignore
                iccid="1234564122445222112",  # type: ignore
                msisdn="**********",  # type: ignore
                request_type="Reallocation",
                prior_value="1",
                new_value="2",
                client_ip="127.0.0.1",
                created_by="<EMAIL>",  # type: ignore
                prior_rate_plan=12,
                new_rate_plan=13,
                allocation_date="2024-06-14 15:43:00",  # type: ignore
            )
        ]
        try:
            headers = {"Content-Type": "application/json"}
            cdr_url = f"{settings.APP_BASE_URL}{self.REALLOCATION_URL}"

            response = requests.post(
                url=cdr_url,
                headers=headers,
                json=jsonable_encoder(audit_details),
                timeout=settings.TIMEOUT,
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def add_allocation_audit_api(self, audit_details: list[model.SIMCardAudit]):
        audit_details = [
            model.SIMCardAudit(
                imsi="***************",  # type: ignore
                iccid="1234564122445222112",  # type: ignore
                msisdn="**********",  # type: ignore
                request_type="Allocation",
                prior_value="1",
                new_value="2",
                client_ip="127.0.0.1",
                created_by="<EMAIL>",  # type: ignore
            )
        ]
        try:
            headers = {"Content-Type": "application/json"}
            cdr_url = f"{settings.APP_BASE_URL}{self.ALLOCATION_URL}"

            response = requests.post(
                url=cdr_url,
                headers=headers,
                json=jsonable_encoder(audit_details),
                timeout=settings.TIMEOUT,
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def add_sim_audit_api(self, audit_details: list[model.SIMCardAudit]):
        audit_details = [
            model.SIMCardAudit(
                imsi="***************",  # type: ignore
                iccid="1234564122445222112",  # type: ignore
                msisdn="**********",  # type: ignore
                request_type="Get status",
                prior_value="1",
                new_value="2",
                client_ip="127.0.0.1",
                created_by="<EMAIL>",  # type: ignore
                audit_date="2024-06-14 15:43:00",  # type: ignore
                message="System",
                status="Deactivated",
                work_id="234588560667861",
                prior_status="READY_FOR_ACTIVATION",
            )
        ]
        try:
            headers = {"Content-Type": "application/json"}
            cdr_url = f"{settings.APP_BASE_URL}{self.SIM_AUDIT_URL}"

            response = requests.post(
                url=cdr_url,
                headers=headers,
                json=jsonable_encoder(audit_details),
                timeout=settings.TIMEOUT,
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def add_sim_provider_audit_api(self, audit_details: model.SIMCardProviderAudit):
        audit_details = [
            model.SIMCardProviderAudit(
                activity_id="1242343242331233",
                created_by="<EMAIL>",  # type: ignore
                audit_date="2024-06-14 15:43:00",  # type: ignore
                message="System",
                status="Deactivated",
                work_id="234588560667861",
                prior_status="READY_FOR_ACTIVATION",
            )
        ]
        try:
            headers = {"Content-Type": "application/json"}
            cdr_url = f"{settings.APP_BASE_URL}{self.SIM_PROVIDER_AUDIT_URL}"

            response = requests.post(
                url=cdr_url,
                headers=headers,
                json=jsonable_encoder(audit_details),
                timeout=settings.TIMEOUT,
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")

    def get_account_audit_api(
        self,
        imsi: list[IMSI],
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        ...

    def get_sim_audit_api(
        self,
        imsi: IMSI,
        month: Month,
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        ...

    def get_sim_pending_info_audit_api(self, imsi: IMSI):
        ...

    def get_sim_provider_log_audit_api(self, work_id: str):
        ...

    def validate_notification(
        self, sim_provider_log: model.SIMProviderLog, sim_status: str
    ) -> bool:
        ...

    def add_upload_msisdn_audit_api(
        self, audit_details: list[model.MsisdnPoolAudit] | list[model.SIMCardAudit]
    ):
        audit_details = [
            model.SIMCardAudit(
                uuid=uuid.uuid4(),
                imsi=IMSI("522545263585296"),
                iccid=ICCID("52254526358529600001"),
                msisdn=MSISDN("***************"),
                request_type="Update MSISDN",
                prior_value="Warehouse",
                new_value="***************",
                field="msisdn",
                action="Updated",
                client_ip="127.0.0.1",
                created_by="<EMAIL>",
            )
        ]
        try:
            headers = {"Content-Type": "application/json"}
            cdr_url = f"{settings.APP_BASE_URL}{self.SIM_MSISDN_POOL_URL}"
            logger.info(
                f"cdr url:- {cdr_url} headers:-{headers}, "
                f"audit details:- {audit_details}"
            )
            return {
                "id": [
                    "67c7fc9bb9a8a05ef8baac0c",
                    "67c7fc9bb9a8a05ef8baac0d",
                    "67c7fc9bb9a8a05ef8baac0e",
                    "67c7fc9bb9a8a05ef8baac0f",
                    "67c7fc9bb9a8a05ef8baac10",
                ]
            }

        except ConnectionError:
            None

    def get_sim_msisdn_update_log_audit_api(self):
        ...

    def add_account_audit_log(
        self, audit_detail: model.AccountActivityLog
    ) -> model.AccountAuditReponse:
        logger.info(f"Account Activity Log: {audit_detail}")
        url = f"{settings.APP_BASE_URL}{self.ACCOUNT_ACTIVITY_URL}"
        logger.info(f"ACCOUNT_ACTIVITY_LOG_URL: {url}")
        return model.AccountAuditReponse(str(uuid.uuid4()))

    def add_uploaded_file_status_audit_api(self, file_details: model.UploadedFileAudit):
        file_details = model.UploadedFileAudit(
            request_id=uuid.uuid4(),
            user="<EMAIL>",
            client_ip="127.0.0.1",
            action="test",
            field="test",
            status="test",
            payload=model.UploadedFileStatus(
                source_endpoint="test",
                message="test",
                success_count=1,
                failure_count=1,
                created_at=datetime.today(),
            ),
        )
        try:
            headers = {"Content-Type": "application/json"}
            audit_url = f"{settings.APP_BASE_URL}{self.UPLOADED_FILE_STATUS_URL}"
            logger.info(
                f"audit_url:- {audit_url} headers:-{headers}, "
                f"audit details:- {file_details}"
            )
            return True

        except ConnectionError:
            None

    def add_sim_action_audit_api(self, sim_action_audit_details: model.SIMActionAudit):
        sim_action_audit_details = model.SIMActionAudit(
            imsi="***************",  # type: ignore
            iccid="1234564122445222112",  # type: ignore
            msisdn="**********",  # type: ignore
            request_type="Flush",
            field="SIM State",
            action="Flush",
            client_ip="127.0.0.1",
            created_by="<EMAIL>",  # type: ignore
        )

        try:
            headers = {"Content-Type": "application/json"}
            cdr_url = f"{settings.APP_BASE_URL}{self.SIM_ACTION_AUDIT_URL}"

            response = requests.post(
                url=cdr_url,
                headers=headers,
                json=jsonable_encoder(sim_action_audit_details),
                timeout=settings.TIMEOUT,
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise AuditAPIError
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Audit API")
