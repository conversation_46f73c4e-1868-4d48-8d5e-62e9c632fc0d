import os
import re
from io import BytesIO
from operator import attrgetter
from typing import Counter, Iterable

import pandas as pd
import pydantic

from app.config import logger, settings
from common.constants import IMSI_FIELD
from common.parser import BaseCSVParser, ParsingError
from common.types import IMSI, FormFactorCode, IPAddress, form_factor_mapping
from sim import exceptions
from sim.domain.model import EID, ICCID, MSISDN, PIN, PUK, AllocationCSVData, SIMCard


class SIMCardCSVParser(BaseCSVParser):
    REQUIRED_FIELDNAMES: tuple = ("ICCID", "IMSI", "MSISDN")

    def _parse_row(self, row: dict[str, str]) -> SIMCard:
        try:
            return SIMCard(
                iccid=ICCID(row["ICCID"]),
                msisdn=MSISDN(row["MSISDN"]),
                imsi=IMSI(row["IMSI"]),
                pin_1=PIN(row["PIN1"]) if row.get("PIN1") else None,
                pin_2=PIN(row["PIN2"]) if row.get("PIN2") else None,
                puk_1=PUK(row["PUK1"]) if row.get("PUK1") else None,
                puk_2=PUK(row["PUK2"]) if row.get("PUK2") else None,
            )
        except pydantic.ValidationError as error:
            logger.error(f" error: {str(error)}")
            raise ParsingError(f"Invalid data for row: {row}")


class EIDCSVParser(BaseCSVParser):
    REQUIRED_FIELDNAMES: tuple = ("ICCID", "IMSI", "MSISDN", "EID")

    def _parse_row(self, row: dict[str, str]) -> SIMCard:
        try:
            return SIMCard(
                iccid=ICCID(row["ICCID"]),
                msisdn=MSISDN(row["MSISDN"]),
                imsi=IMSI(row["IMSI"]),
                sim_eid=EID(row["EID"]),
                pin_1=PIN(row["PIN1"]) if row.get("PIN1") else None,
                pin_2=PIN(row["PIN2"]) if row.get("PIN2") else None,
                puk_1=PUK(row["PUK1"]) if row.get("PUK1") else None,
                puk_2=PUK(row["PUK2"]) if row.get("PUK2") else None,
            )

        except pydantic.ValidationError as error:
            logger.error(f" error: {str(error)}")
            raise ParsingError(f"Invalid data for row: {row}")


class ImsiCSVParser(BaseCSVParser):
    REQUIRED_FIELDNAMES: tuple = ("IMSI",)

    def _parse_row(self, row: dict[str, str]) -> IMSI:
        imsi_value = row.get("IMSI", None)

        if imsi_value is None:
            raise ParsingError(f"Missing 'IMSI' field in row: {row}")

        return IMSI(imsi_value)


class AllocationCSVParser(BaseCSVParser):
    REQUIRED_FIELDNAMES: tuple = ("IMSI",)
    OPTIONAL_FIELDS: tuple = ("IP", "APN")

    def _parse_row(self, row: dict[str, str]):
        imsi_value = row.get("IMSI", None)

        if imsi_value is None:
            raise ParsingError(f"Missing 'IMSI' field in row: {row}")

        ip_value = row.get("IP", None)
        apn_value = row.get("APN", None)

        ip = IPAddress(ip_value) if ip_value else None
        apn = None
        if apn_value:
            try:
                apn = int(apn_value)
            except ValueError:
                raise ParsingError(f"APN value must be an integer in row: {row}")

        return AllocationCSVData(
            imsi=IMSI(imsi_value),
            ip=ip,
            apn_id=apn,
        )


class FileNameParser:
    ALLOWED_EXTENSIONS: set = {".csv"}

    def _parse_file_name(self, file_name, file):
        base_name, file_extension = os.path.splitext(file_name)

        if not file_extension or file_extension.lower() not in self.ALLOWED_EXTENSIONS:
            raise ParsingError("File extension invalid, only .csv file supported")

        # Prepare regex with valid codes
        allowed_prefixes = [code.value for code in FormFactorCode]
        pattern = r"^(" + "|".join(re.escape(code) for code in allowed_prefixes) + r")_"

        if not re.match(pattern, base_name):
            raise ParsingError(
                f"Invalid filename. It must start with one of the "
                "following form factor "
                f": {' , '.join(code + '_' for code in allowed_prefixes)}"
            )

        file_code = next(
            (
                code
                for code in sorted(
                    FormFactorCode, key=lambda c: len(c.value), reverse=True
                )
                if base_name.startswith(code.value + "_")
            ),
            None,
        )
        form_factor_code = form_factor_mapping.get(file_code)
        logger.info(f"allocation_parse_file_name form factor code:{form_factor_code}")

        file_size = round(os.fstat(file.fileno()).st_size / (1024 * 1024), 3)

        if file_size > settings.MAX_FILE_SIZE_MB:  # type: ignore
            raise ParsingError(f"File '{file_name}' size should not exceed 5MB")

        line = file.readline().decode("utf-8")
        if line.count(IMSI_FIELD) > 1:
            raise ParsingError("More than one occurrence of 'IMSI' column in file")
        file.seek(0)
        return form_factor_code


def validate_column_length(column, min_length, max_length, col):
    if column.str.len().lt(min_length).any() or column.str.len().gt(max_length).any():
        raise ParsingError(
            f"{col} column must be between {min_length} and {max_length} characters"
        )


def validate_file_extension(file_name: str) -> bool:
    ALLOWED_EXTENSIONS: set = {".csv"}
    _, file_extension = os.path.splitext(file_name)
    if not file_extension or file_extension.lower() not in ALLOWED_EXTENSIONS:
        raise ParsingError("File extension invalid, only .csv file supported")
    return True


def parse_excel_file(file, file_stream) -> pd.DataFrame:
    # Convert file stream to BytesIO
    file_stream = BytesIO(file_stream)

    # Check file extension
    if not file.filename.endswith((".xlsx", ".xls")):
        raise ParsingError("Invalid file format. Please upload an Excel file.")

    # Check filename
    if file.filename != "MSISDN_UPDATE_MAIN.xlsx":
        raise ParsingError(
            "Invalid filename it should be named 'MSISDN_UPDATE_MAIN.xlsx'.",
        )

    # Attempt to read the Excel file
    try:
        excel_data = pd.read_excel(file_stream, sheet_name=0, dtype=str)
    except Exception as e:
        raise ParsingError("An error occurred while reading the Excel file") from e

    # Check for required columns
    required_columns = {"IMSI", "MSISDN", "ICCID"}
    if not required_columns.issubset(excel_data.columns):
        raise ParsingError(
            "Excel file must contain 'IMSI', 'MSISDN', and 'ICCID' columns.",
        )

    # Check if columns contain data
    for column in required_columns:
        if excel_data[column].isnull().all():
            raise ParsingError(
                f"The '{column}' column is present but contains no data.",
            )

    # Check for rows with null values
    if excel_data.isnull().any(axis=1).any():
        raise ParsingError(
            "Excel file contains rows with null values.",
        )

    # Check for duplicates in the 'MSISDN' column
    if excel_data["MSISDN"].duplicated().any():
        raise ParsingError(
            "Excel file contains duplicate values in the 'MSISDN' column.",
        )

    # Validate length of 'MSISDN' and 'ICCID' columns
    validate_column_length(
        excel_data["MSISDN"], MSISDN.min_length, MSISDN.max_length, "MSISDN"
    )
    validate_column_length(
        excel_data["ICCID"], ICCID.min_length, ICCID.max_length, "ICCID"
    )

    validate_column_length(excel_data["IMSI"], IMSI.min_length, IMSI.max_length, "IMSI")

    return excel_data


def _validate_csv_file(file, file_stream):
    file_size = round(len(file_stream) / (1024 * 1024), 3)

    if settings.MAX_FILE_SIZE_MB is None:
        raise ParsingError("Environment variable MAX_FILE_SIZE_MB is not set.")

    if file_size > settings.MAX_FILE_SIZE_MB:  # type: ignore
        raise ParsingError("File size exceeds the maximum limit of 5MB.")

    file_stream = BytesIO(file_stream)

    if not file.filename.endswith(".csv"):
        raise ParsingError("Invalid file format. Please upload a CSV file.")


def parse_msisdn_file(
    file, file_stream
) -> tuple[list[MSISDN], list[str], list[MSISDN], int]:

    _validate_csv_file(file=file, file_stream=file_stream)

    try:
        csv_data = pd.read_csv(BytesIO(file_stream), dtype=str)
    except Exception as e:
        raise ParsingError("An error occurred while reading the CSV file.") from e

    required_columns = {"MSISDN"}
    if set(csv_data.columns) != required_columns:
        raise ParsingError("CSV file must contain only the 'MSISDN' column.")

    if csv_data["MSISDN"].isnull().all():
        raise ParsingError("The 'MSISDN' column is present but contains no data.")

    if csv_data["MSISDN"].isnull().any():
        raise ParsingError(
            "CSV file contains rows with null values in the 'MSISDN' column."
        )

    msisdn_series = csv_data["MSISDN"].dropna()

    # Separate valid and invalid MSISDNs based on numeric check and length
    valid_msisdn_series = msisdn_series[
        msisdn_series.apply(
            lambda msisdn: msisdn.isdigit()
            and MSISDN.min_length <= len(msisdn) <= MSISDN.max_length
        )
    ]

    invalid_msisdn = msisdn_series[
        ~msisdn_series.apply(
            lambda msisdn: msisdn.isdigit()
            and MSISDN.min_length <= len(msisdn) <= MSISDN.max_length
        )
    ].tolist()  # type: ignore

    # Count occurrences of each valid MSISDN
    msisdn_counts = valid_msisdn_series.value_counts()  # type: ignore

    # Unique valid MSISDNs (keep only one occurrence)
    unique_valid_msisdn = msisdn_counts.index.tolist()

    # Duplicate MSISDNs (get extra occurrences by subtracting unique set)
    duplicate_msisdn = valid_msisdn_series[
        valid_msisdn_series.duplicated()  # type: ignore
    ].tolist()

    total = len(unique_valid_msisdn) + len(invalid_msisdn) + len(duplicate_msisdn)
    return unique_valid_msisdn, invalid_msisdn, duplicate_msisdn, total


def parse_imsi_msisdn_csv(file, file_stream):
    _validate_csv_file(file=file, file_stream=file_stream)

    try:
        csv_data = pd.read_csv(BytesIO(file_stream), dtype=str)
    except Exception as e:
        raise ParsingError("An error occurred while reading the CSV file.") from e

    required_columns = {"IMSI", "MSISDN"}
    if set(csv_data.columns) != required_columns:
        raise ParsingError(
            "CSV file must contain only the 'IMSI' and 'MSISDN' columns."
        )

    total_records = len(csv_data)  # Total records in CSV

    if csv_data.isnull().all().all():
        raise ParsingError(
            "The 'IMSI' and 'MSISDN' columns are present but contain no data."
        )

    # Drop rows where IMSI or MSISDN is missing
    csv_data.dropna(subset=["IMSI", "MSISDN"], inplace=True)

    # Validate IMSI and MSISDN formats
    valid_data = csv_data[
        csv_data["IMSI"].apply(
            lambda x: x.isdigit() and IMSI.min_length <= len(x) <= IMSI.max_length
        )
        & csv_data["MSISDN"].apply(
            lambda x: x.isdigit() and MSISDN.min_length <= len(x) <= MSISDN.max_length
        )
    ]

    valid_imsi_list = valid_data["IMSI"].tolist()
    valid_msisdn_list = valid_data["MSISDN"].tolist()

    invalid_data = csv_data[~csv_data.index.isin(valid_data.index)]

    # Detect duplicates
    duplicate_imsi = valid_data[valid_data.duplicated(subset=["IMSI"], keep=False)][
        "IMSI"
    ].tolist()
    duplicate_msisdn = valid_data[valid_data.duplicated(subset=["MSISDN"], keep=False)][
        "MSISDN"
    ].tolist()

    # Invalid records
    invalid_records = invalid_data.to_dict(orient="records")

    return (
        total_records,
        invalid_records,
        duplicate_imsi,
        duplicate_msisdn,
        valid_imsi_list,
        valid_msisdn_list,
        valid_data,
    )


def validate_sim_cards(sim_cards: Iterable[SIMCard]) -> list[SIMCard]:
    sorted_sim_cards = list(sorted(sim_cards, key=attrgetter("imsi")))
    imsi_list = [s.imsi for s in sorted_sim_cards]
    non_unique_imsi = [k for (k, v) in Counter(imsi_list).items() if v > 1]
    if non_unique_imsi:
        raise exceptions.ImsisAreNotUnique("Non-unique IMSI(s) found in CSV file.")

    msisdn_list = [s.msisdn for s in sorted_sim_cards]
    duplicate_msisdn = [k for (k, v) in Counter(msisdn_list).items() if v > 1]
    if duplicate_msisdn:
        raise exceptions.ImsisAreNotUnique("Non-unique MSISDN(s) found in CSV file.")

    imsi_first, imsi_last = sorted_sim_cards[0].imsi, sorted_sim_cards[-1].imsi
    not_continuity_imsi = set(range(int(imsi_first), int(imsi_last) + 1)) - set(
        map(int, imsi_list)
    )
    if not_continuity_imsi:
        raise exceptions.ImsisAreNotContinuity(
            "There is no continuous range of IMSI in the CSV file."
        )

    return sorted_sim_cards


def validate_sim_cards_and_eid(sim_cards: Iterable[SIMCard]):
    sorted_sim_cards = validate_sim_cards(sim_cards)
    eid_list = [EID(s.sim_eid) for s in sorted_sim_cards]
    duplicate_eid = [k for (k, v) in Counter(eid_list).items() if v > 1]
    if duplicate_eid:
        raise exceptions.EIDsAreNotUnique("Non-unique EID(s) found in CSV file.")

    return sorted_sim_cards
