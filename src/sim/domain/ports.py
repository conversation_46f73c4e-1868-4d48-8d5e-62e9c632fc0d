from abc import ABC, abstractmethod

from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import IMSI, Month
from sim.domain import model
from sim.domain.model import (
    MSISDN,
    MarketShareModel,
    SIMFlushResponse,
    SIMPODResponse,
    SIMProviderLog,
    SIMSendSMSResponse,
    SimStatus,
)


class AbstractSIMProvisioningAPI(ABC):
    @abstractmethod
    def sim_status(self, imsi: IMSI, msisdn_: MSISDN) -> model.SIMStatusResponse:
        ...

    @abstractmethod
    def activate_sim(
        self,
        imsi: IMSI,
        msisdn_: MSISDN,
        prior_status: SimStatus,
        valid_sim_profile: str,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        ...

    @abstractmethod
    def suspend_sim(
        self,
        msisdn_: MSISDN,
        prior_status: SimStatus,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        ...

    @abstractmethod
    def sim_workitem_status(self, work_id: str) -> model.SIMWorkItemStatusResponse:
        ...

    @abstractmethod
    def flush_sim(
        self,
        imsi: IMSI,
        msisdn_: MSISDN,
        client_ip: str | None = None,
    ) -> SIMFlushResponse:
        ...

    @abstractmethod
    def pod_sim(
        self,
        imsi: IMSI,
        msisdn_: MSISDN,
        client_ip: str | None = None,
    ) -> SIMPODResponse:
        ...

    @abstractmethod
    def sms_sim_ppl(
        self,
        imsi: IMSI,
        msisdn_: MSISDN,
        message: str,
        client_ip: str | None = None,
    ) -> SIMSendSMSResponse:
        ...

    @abstractmethod
    def apn_sim(
        self,
        imsi: IMSI,
        msisdn_: MSISDN,
        apn_id: str,
        sim_apn_action: model.SIMAPNAction,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        ...

    @abstractmethod
    def get_latest_data_session(
        self,
        iccid: str,
    ) -> model.DataSession:
        ...

    @abstractmethod
    def get_location(
        self,
        imsi: str,
        page_number: int = 1,
        page_size: int = 100,
    ) -> model.LocationResponse:
        ...


class AbstractMarketShareAPI(ABC):
    @abstractmethod
    def market_share_usage(
        self, request_data: MarketShareModel
    ) -> model.MarketShareCarrier:
        ...

    @abstractmethod
    def get_imsis_usage(self, marketshare: model.MarketShareModel) -> model.MarketShare:
        ...


class AbstractAuditService(ABC):
    @abstractmethod
    def add_reallocation_audit_api(self, audit_details: list[model.SIMCardAudit]):
        ...

    @abstractmethod
    def add_allocation_audit_api(self, audit_details: list[model.SIMCardAudit]):
        ...

    @abstractmethod
    def add_sim_audit_api(self, audit_details: list[model.SIMCardAudit]):
        ...

    @abstractmethod
    def add_sim_provider_audit_api(self, audit_details: model.SIMCardProviderAudit):
        ...

    @abstractmethod
    def get_sim_pending_info_audit_api(self, imsi: IMSI):
        ...

    @abstractmethod
    def get_sim_provider_log_audit_api(self, work_id: str):
        ...

    @abstractmethod
    def get_sim_audit_api(
        self,
        imsi: IMSI,
        month: Month,
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        ...

    @abstractmethod
    def get_account_audit_api(
        self,
        imsi: list[IMSI],
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        ...

    @abstractmethod
    def validate_notification(
        self, sim_provider_log: model.SIMProviderLog, sim_status: str
    ) -> bool:
        ...

    @abstractmethod
    def add_upload_msisdn_audit_api(
        self, audit_details: list[model.MsisdnPoolAudit] | list[model.SIMCardAudit]
    ):
        ...

    @abstractmethod
    def get_sim_msisdn_update_log_audit_api(self):
        ...

    @abstractmethod
    def add_account_audit_log(
        self, audit_detail: model.AccountActivityLog
    ) -> model.AccountAuditReponse:
        ...

    @abstractmethod
    def add_sim_action_audit_api(self, audit_details: model.SIMActionAudit):
        ...

    @abstractmethod
    def add_uploaded_file_status_audit_api(self, file_details: model.UploadedFileAudit):
        ...
