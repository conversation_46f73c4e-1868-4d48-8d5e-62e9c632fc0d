import uuid
from datetime import date, datetime
from typing import Annotated, Any, List, Optional

from fastapi import Form, UploadFile
from pydantic import (
    Field,
    NonNegativeInt,
    PositiveInt,
    create_model,
    parse_obj_as,
    root_validator,
)

from api.schema_types import IMSI, CamelBaseModel, HumanReadableBaseModel
from api.sim.examples import (
    _NR_MICRO_RANGE,
    ALLOCATIONS,
    BULK_UPDATE_REQUEST,
    CREATE_ALLOCATION_REQUEST,
    CREATE_RANGE_REQUEST,
    CREATE_RANGE_RESPONSE,
    FREE_MSISDN_COUNT_DETAILS,
    IMSI_RANGE_REQUEST,
    ISMI_DELETE_MESSAGE,
    MSISDN_POOL,
    MSISDN_POOL_DETAILS,
    NOTIFICATION_REQUEST,
    RANGES,
    SIM,
    SIM_ACTIVATION_REQUEST,
    SIM_APN_REQUEST,
    SIM_MSISDN_COUNT_DETAILS,
    <PERSON>IM_MSISDN_RESULT,
    SIM_SMS_REQUEST,
    UNALLOCATED_SIM_CARDS,
    UPLOAD_MSISDN_RESULT,
)
from common.types import IMSI as CIMSI
from common.types import MSISDN as CMSISDN
from common.types import Carrier, Enumeration, FormFactor
from sim.domain import model
from sim.domain.model import EID, ICCID, MSISDN, MSISDNFactor


class UploadSimCardsRequest(CamelBaseModel):
    title: str = Form("Reference")
    form_factor: FormFactor = Form(...)
    file: UploadFile = Form(...)


class CreateRangeRequest(CamelBaseModel):
    title: str
    form_factor: FormFactor

    class Config:
        schema_extra = {"example": CREATE_RANGE_REQUEST}


parse_obj_as(CreateRangeRequest, CREATE_RANGE_REQUEST)


class CreateRangeResponse(CreateRangeRequest):
    id: int
    provider: str
    created_at: datetime
    created_by: str

    class Config:
        schema_extra = {"example": CREATE_RANGE_RESPONSE}
        orm_mode = True


parse_obj_as(CreateRangeResponse, CREATE_RANGE_RESPONSE)


class Range(CamelBaseModel):
    id: int
    title: str
    provider: str
    form_factor: str
    quantity: NonNegativeInt
    imsi_first: IMSI | None
    imsi_last: IMSI | None
    remaining: NonNegativeInt
    created_at: datetime
    created_by: str

    class Config:
        schema_extra = {"example": RANGES[0]}
        orm_mode = True

    @classmethod
    def from_model(cls, range_details: model.Range) -> "Range":
        return cls(
            id=range_details.id,
            title=range_details.title,
            provider=range_details.provider,
            form_factor=range_details.form_factor,
            quantity=range_details.quantity,
            imsi_first=range_details.imsi_first,
            imsi_last=range_details.imsi_last,
            remaining=range_details.remaining,
            created_at=range_details.created_at,
            created_by=range_details.created_by,
        )


class Allocation(CamelBaseModel):
    id: int
    title: str
    account_id: int
    range_id: int
    imsi: IMSI | None
    quantity: NonNegativeInt | None
    created_at: datetime

    @classmethod
    def from_model(cls, allocation: model.Allocation) -> "Allocation":
        return cls(
            id=allocation.id,
            title=allocation.title,
            account_id=allocation.account_id,
            range_id=allocation.range_id,
            imsi=allocation.imsi,
            created_at=allocation.created_at,
        )

    class Config:
        schema_extra = {"example": ALLOCATIONS[0]}
        orm_mode = True


parse_obj_as(list[Allocation], ALLOCATIONS)


class CreateAllocation(CamelBaseModel):
    title: str
    account_id: int
    range_id: int
    rate_plan_id: int
    quantity: NonNegativeInt

    class Config:
        schema_extra = {"example": CREATE_ALLOCATION_REQUEST}

    def to_model(self) -> model.Allocation:
        return model.Allocation(
            title=self.title,
            account_id=self.account_id,
            range_id=self.range_id,
            rate_plan_id=self.rate_plan_id,
            quantity=self.quantity,  # type: ignore
        )


parse_obj_as(CreateAllocation, CREATE_ALLOCATION_REQUEST)


class SIMCard(CamelBaseModel):
    id: int
    range_id: int
    iccid: ICCID
    imsi: IMSI
    msisdn: MSISDN
    form_factor: FormFactor
    sim_profile: model.SimProfile | None = None
    allocation_id: int | None = None
    rate_plan_id: int | None = None
    status: str = "Unknown"

    class Config:
        schema_extra = {"example": SIM}
        orm_mode = True

    @classmethod
    def from_model(cls, sim_card: model.SIMCard) -> "SIMCard":
        return cls(
            id=sim_card.id,
            range_id=sim_card.range_id,
            iccid=sim_card.iccid,
            imsi=sim_card.imsi,
            msisdn=sim_card.msisdn,
            form_factor=sim_card.form_factor,
            allocation_id=sim_card.allocation_id,
            rate_plan_id=sim_card.rate_plan_id,
            sim_profile=sim_card.sim_profile,
        )


parse_obj_as(SIMCard, SIM)


class SIMCardExport(HumanReadableBaseModel):
    iccid: ICCID = Field(alias="ICCID")
    msisdn: MSISDN = Field(alias="MSISDN")
    imsi: IMSI = Field(alias="IMSI")
    type: FormFactor
    allocation_reference: str
    allocation_date: datetime | None = None
    rate_plan: NonNegativeInt
    status: str

    @classmethod
    def from_response_model(cls, sim_card: SIMCard) -> "SIMCardExport":
        return cls(
            iccid=sim_card.iccid,
            imsi=sim_card.imsi,
            msisdn=sim_card.msisdn,
            type=sim_card.form_factor,
            allocation_reference=f"Allocation {sim_card.allocation_id}",
            rate_plan=sim_card.rate_plan_id,  # We have only Rate Plan ID
            status=sim_card.status,
        )


class IMSIRangeCSVRequest(CamelBaseModel):
    imsi_first: IMSI
    length: PositiveInt


parse_obj_as(IMSIRangeCSVRequest, IMSI_RANGE_REQUEST)

form_factor_fields: dict[str, Any] = {
    ffactor.value.lower(): (int, 0) for ffactor in FormFactor
}
SIMCardsRemains = create_model(
    "SIMCardsRemains", provider=(str, "NR"), **form_factor_fields
)


class PushIMSI(CamelBaseModel):
    imsi: IMSI
    created_by: str

    class Config:
        schema_extra = {"example": SIM_ACTIVATION_REQUEST}


class SIMStatusResponse(CamelBaseModel):
    reference_id: int
    imsi: IMSI
    msisdn: MSISDN
    sim_status: str


class SIMActivateResponse(CamelBaseModel):
    uuid: str
    message: str
    status: str


class SIMDeactivatedResponse(CamelBaseModel):
    uuid: str
    message: str
    status: str


class ConnectionSummary(CamelBaseModel):
    msisdn: MSISDN
    imsi: IMSI
    iccid: ICCID
    first_activated: date | None
    last_session: datetime | None
    sim_status: model.SimStatus
    rate_plan_id: int
    rate_plan: str | None


class SimUsage(CamelBaseModel):
    sim_id: int
    iccid: ICCID
    msisdn: MSISDN
    imsi: IMSI
    eid: EID | None
    type: FormFactor
    allocation_reference: str
    allocation_date: datetime | None
    rate_plan: str
    usage: int
    sim_status: model.SimStatus
    ee_usage: int
    sim_profile: Enumeration
    msisdn_factor: Enumeration

    @classmethod
    def from_model(cls, sim_usage: model.SimUsage) -> "SimUsage":
        return cls(
            sim_id=sim_usage.sim_id,
            iccid=sim_usage.iccid,
            msisdn=sim_usage.msisdn,
            imsi=sim_usage.imsi,
            eid=sim_usage.eid,
            type=sim_usage.type,
            allocation_reference=sim_usage.allocation_reference,
            allocation_date=sim_usage.allocation_date,
            rate_plan=sim_usage.rate_plan,
            usage=sim_usage.usage,
            sim_status=sim_usage.sim_status,
            ee_usage=sim_usage.ee_usage,
            sim_profile=Enumeration.from_enum_value(sim_usage.sim_profile),
            msisdn_factor=Enumeration.from_enum_value(sim_usage.msisdn_factor),
        )


class SimUsageExport(HumanReadableBaseModel):
    imsi: IMSI = Field(alias="IMSI")
    iccid: ICCID = Field(alias="ICCID")
    msisdn: MSISDN = Field(alias="MSISDN")
    eid: Optional[EID] = Field(default=None, alias="EID")
    msisdn_type: MSISDNFactor = Field(alias="MSISDN Type")
    sim_profile: model.SimProfile = Field(alias="SIM Profile")
    type: FormFactor = Field(alias="SIM Type")
    allocation_reference: str
    allocation_date: datetime | None = None
    rate_plan: str
    ee_percentage: float = Field(alias="EE%")
    cycle_to_date_traffic_bytes: int = Field(alias="Cycle to Date Traffic (Bytes)")
    sim_status: model.SimStatus = Field(alias="Status")
    ee_usage: int = Field(alias="EE Usage")

    @classmethod
    def from_response_model(cls, sim_usage: model.SimUsage) -> "SimUsageExport":
        # calculate ee percentage
        if sim_usage.usage > 0:
            sim_ee_usage_percentage = round(
                (sim_usage.ee_usage / sim_usage.usage) * 100, 2
            )
        else:
            sim_ee_usage_percentage = 0

        return cls(
            iccid=sim_usage.iccid,
            msisdn=sim_usage.msisdn,
            imsi=sim_usage.imsi,
            eid=sim_usage.eid,
            type=sim_usage.type,
            msisdn_type=sim_usage.msisdn_factor,
            allocation_reference=sim_usage.allocation_reference,
            allocation_date=sim_usage.allocation_date,
            rate_plan=sim_usage.rate_plan,
            cycle_to_date_traffic_bytes=sim_usage.usage,
            sim_status=sim_usage.sim_status,
            ee_usage=sim_usage.ee_usage,
            ee_percentage=sim_ee_usage_percentage,
            sim_profile=sim_usage.sim_profile,
        )


class ActiveSimMonthlyStatistic(CamelBaseModel):
    id: int
    imsi: IMSI
    iccid: ICCID
    msisdn: MSISDN
    sim_status: model.SimStatus
    is_first_activation: bool
    usage: int
    rate_plan_id: int

    @classmethod
    def from_model(
        cls, active_sim_data: model.ActiveSimMonthlyStatistic
    ) -> "ActiveSimMonthlyStatistic":
        return cls(
            id=active_sim_data.id,
            imsi=active_sim_data.imsi,
            iccid=active_sim_data.iccid,
            msisdn=active_sim_data.msisdn,
            sim_status=active_sim_data.sim_status,
            is_first_activation=active_sim_data.is_first_activation,
            usage=active_sim_data.usage,
            rate_plan_id=active_sim_data.rate_plan_id,
        )


class SimCDRHistory(CamelBaseModel):
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    session_starttime: datetime
    session_endtime: datetime
    duration: int
    data_volume: int
    country_name: str
    carrier_name: str

    @classmethod
    def from_model(cls, connection_history: model.SimCDRHistory) -> "SimCDRHistory":
        return cls(
            iccid=connection_history.iccid,
            imsi=connection_history.imsi,
            country=connection_history.country,
            carrier=connection_history.carrier,
            session_starttime=connection_history.session_starttime,
            session_endtime=connection_history.session_endtime,
            duration=connection_history.duration,
            data_volume=connection_history.data_volume,
            country_name=connection_history.country_name,
            carrier_name=connection_history.carrier_name,
        )


class SimCDRHistoryExport(HumanReadableBaseModel):
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    session_starttime: datetime
    session_endtime: datetime
    duration: int
    data_volume: int
    country_name: str
    carrier_name: str

    @classmethod
    def from_response_model(
        cls, connection_history: model.SimCDRHistory
    ) -> "SimCDRHistoryExport":
        return cls(
            iccid=connection_history.iccid,
            imsi=connection_history.imsi,
            country_name=connection_history.country_name,
            carrier_name=connection_history.carrier_name,
            session_starttime=connection_history.session_starttime,
            session_endtime=connection_history.session_endtime,
            duration=connection_history.duration,
            data_volume=connection_history.data_volume,
            country=connection_history.country,
            carrier=connection_history.carrier,
        )


class AuditLogs(CamelBaseModel):
    field: str
    prior_value: model.SimStatus | None
    new_value: str | None
    date: datetime
    user_name: str

    @classmethod
    def from_model(cls, audit_logs: model.AuditLogs) -> "AuditLogs":
        return cls(
            field=audit_logs.field,
            prior_value=audit_logs.prior_value,
            new_value=audit_logs.new_value,
            date=audit_logs.date,
            user_name=audit_logs.user_name,
        )


class MonthUsage(CamelBaseModel):
    total_usage: int
    total_sims: int
    total_active_sims: int
    total_deactivated_sims: int
    total_pending_sims: int
    total_ready_activation_sims: int


class SimStatusDetails(CamelBaseModel):
    iccid: ICCID
    imsi: IMSI
    msisdn: MSISDN
    sim_status: model.SimStatus

    @classmethod
    def from_model(cls, sim_statusinfo: model.SimStatusDetails) -> "SimStatusDetails":
        return cls(
            iccid=sim_statusinfo.iccid,
            imsi=sim_statusinfo.imsi,
            msisdn=sim_statusinfo.msisdn,
            sim_status=sim_statusinfo.sim_status,
        )


class SIMWorkItemStatusResponse(CamelBaseModel):
    reference: str
    audit_date: datetime
    request_type: model.RequestType
    status: model.NotificationStatus
    message: str


class NotificationBase(CamelBaseModel):
    """Base Notification Model"""

    message: str = Field(..., alias="Message")
    status: model.NotificationStatus = Field(..., alias="Status")


class Manxm2m(NotificationBase):
    """Manxm2m Notification Model"""

    name: str = Field(..., alias="Name")
    work_item_id: uuid.UUID = Field(..., alias="WorkItemId")


class MAP(NotificationBase):
    """MAP Notification Model"""

    name: str = Field(..., alias="Name")


class PIP(NotificationBase):
    """PIP Notification Model"""

    name: str = Field(..., alias="Name")


class Notification(NotificationBase):
    """Notification Request Body"""

    results: Optional[List[Manxm2m | MAP | PIP]] = Field(alias="Results", default=None)
    reference: str = Field(..., alias="Reference")
    audit_date: datetime = Field(..., alias="AuditDate")
    request_type: str = Field(..., alias="RequestType")
    work_item_id: str = Field(..., alias="WorkItemId")

    def to_model(self) -> model.SIMProviderLog:
        return model.SIMProviderLog(
            sim_activity_log_uuid=str(uuid.uuid4()),
            activity_id=str(uuid.uuid4()),
            audit_date=self.audit_date,
            message=self.message,
            status=self.status,
            work_id=self.work_item_id,
            prior_status=model.SimStatus.PENDING,
        )

    class Config:
        schema_extra = {"example": NOTIFICATION_REQUEST}


class PushBulkIMSI(CamelBaseModel):
    imsi: list[CIMSI] = Field(min_items=1)
    created_by: str


class SimVoiceCDRHistory(CamelBaseModel):
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    call_date: datetime
    call_number: str
    call_minutes: int
    country_name: str
    carrier_name: str

    @classmethod
    def from_model(
        cls, voice_connection_history: model.SimVoiceCDRHistory
    ) -> "SimVoiceCDRHistory":
        return cls(
            iccid=voice_connection_history.iccid,
            imsi=voice_connection_history.imsi,
            country=voice_connection_history.country,
            carrier=voice_connection_history.carrier,
            call_date=voice_connection_history.call_date,
            call_number=voice_connection_history.call_number,
            call_minutes=voice_connection_history.call_minutes,
            country_name=voice_connection_history.country_name,
            carrier_name=voice_connection_history.carrier_name,
        )


class SimVoiceCDRHistoryExport(CamelBaseModel):
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    call_date: datetime
    call_number: str
    call_minutes: int
    country_name: str
    carrier_name: str

    @classmethod
    def from_response_model(
        cls, voice_connection_history: model.SimVoiceCDRHistory
    ) -> "SimVoiceCDRHistoryExport":
        return cls(
            iccid=voice_connection_history.iccid,
            imsi=voice_connection_history.imsi,
            country=voice_connection_history.country,
            carrier=voice_connection_history.carrier,
            call_date=voice_connection_history.call_date,
            call_number=voice_connection_history.call_number,
            call_minutes=voice_connection_history.call_minutes,
            country_name=voice_connection_history.country_name,
            carrier_name=voice_connection_history.carrier_name,
        )


class SimSMSCDRHistory(CamelBaseModel):
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    date_sent: datetime
    sent_from: str
    sent_to: str
    country_name: str
    carrier_name: str

    @classmethod
    def from_model(
        cls, sms_connection_history: model.SimSMSCDRHistory
    ) -> "SimSMSCDRHistory":
        return cls(
            iccid=sms_connection_history.iccid,
            imsi=sms_connection_history.imsi,
            country=sms_connection_history.country,
            carrier=sms_connection_history.carrier,
            date_sent=sms_connection_history.date_sent,
            sent_from=sms_connection_history.sent_from,
            sent_to=sms_connection_history.sent_to,
            country_name=sms_connection_history.country_name,
            carrier_name=sms_connection_history.carrier_name,
        )


class SimSMSCDRHistoryExport(CamelBaseModel):
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    date_sent: datetime
    sent_from: str
    sent_to: str
    country_name: str
    carrier_name: str

    @classmethod
    def from_response_model(
        cls, sms_connection_history: model.SimSMSCDRHistory
    ) -> "SimSMSCDRHistoryExport":
        return cls(
            iccid=sms_connection_history.iccid,
            imsi=sms_connection_history.imsi,
            country=sms_connection_history.country,
            carrier=sms_connection_history.carrier,
            date_sent=sms_connection_history.date_sent,
            sent_from=sms_connection_history.sent_from,
            sent_to=sms_connection_history.sent_to,
            country_name=sms_connection_history.country_name,
            carrier_name=sms_connection_history.carrier_name,
        )


class UploadCustomIMSI(CamelBaseModel):
    allocation_reference: str = Form("allocation_reference")
    account_id: int = Form(...)
    rate_plan_id: int = Form(...)
    file: UploadFile = Form(...)
    sim_profile: model.SimProfile
    msisdn_factor: model.MSISDNFactor


class AllocationSummary(CamelBaseModel):
    id: int
    title: str
    account_id: int
    account_name: str
    quantity: int
    created_at: datetime
    form_factor: FormFactor
    provider: str
    logo_url: str | None = None
    country: str | None = None

    @classmethod
    def from_model(cls, sim_card: model.AllocationSummary) -> "AllocationSummary":
        return cls(
            id=sim_card.id,
            title=sim_card.title,
            account_id=sim_card.account_id,
            account_name=sim_card.account_name,
            quantity=sim_card.quantity,
            created_at=sim_card.created_at,
            form_factor=sim_card.form_factor,
            provider=sim_card.provider,
            logo_url=sim_card.logo_url,
            country=sim_card.country,
        )


class ReAllocation(CamelBaseModel):
    imsi: list[CIMSI] = Field(min_items=1)


class SIMCardAuditLogs(CamelBaseModel):
    id: str
    imsi: IMSI
    request_type: str
    field: str
    action: str
    iccid: str | None
    msisdn: str | None
    prior_value: str | None
    new_value: str | None
    client_ip: str | None = None
    created_by: str | None = None
    created_date: str | None = None

    @classmethod
    def from_model(cls, audit_logs: model.SIMCardAuditLogs) -> "SIMCardAuditLogs":
        return cls(
            id=audit_logs.id,
            imsi=audit_logs.imsi,
            iccid=audit_logs.iccid,
            msisdn=audit_logs.msisdn,
            request_type=audit_logs.request_type,
            prior_value=audit_logs.prior_value,
            new_value=audit_logs.new_value,
            field=audit_logs.field,
            action=audit_logs.action,
            client_ip=audit_logs.client_ip,
            created_by=audit_logs.created_by,
            created_date=audit_logs.created_date,
        )


class MsisdnPoolDetails(CamelBaseModel):
    msisdn: MSISDN
    sim_profile: model.SimProfile
    msisdn_factor: model.MSISDNFactor
    created_at: datetime
    country: str
    sim_provider: str
    uploaded_by: str
    imsi: IMSI | None = None


class MsisdnPool(MsisdnPoolDetails):
    account_name: str | None = None
    logo_key: str | None = None
    logo_url: str | None = None

    class Config:
        schema_extra = {"example": MSISDN_POOL}
        orm_mode = True

    @classmethod
    def from_model(cls, msisdn: model.MsisdnDetails) -> "MsisdnPool":
        return cls(
            msisdn=msisdn.msisdn,
            imsi=msisdn.imsi,
            created_at=msisdn.created_at,
            sim_profile=msisdn.sim_profile,
            msisdn_factor=msisdn.msisdn_factor,
            country="GB",
            sim_provider="NR",
            uploaded_by=msisdn.uploaded_by,
            account_name=msisdn.account_name,
            logo_key=msisdn.logo_key,
            logo_url=msisdn.logo_url,
        )


class MsisdnPoolExport(HumanReadableBaseModel):
    msisdn: MSISDN = Field(alias="MSISDN")
    msisdn_factor: model.MSISDNFactor = Field(alias="MSISDN Type")
    sim_profile: model.SimProfile = Field(alias="SIM Profile")
    country: str
    sim_provider: str = Field(alias="SIM Provider")
    created_at: datetime
    uploaded_by: str

    class Config:
        schema_extra = {"example": MSISDN_POOL_DETAILS}

    @classmethod
    def from_response_model(cls, msisdn: model.MsisdnDetails) -> "MsisdnPoolExport":
        return cls(
            msisdn=msisdn.msisdn,
            created_at=msisdn.created_at,
            sim_profile=msisdn.sim_profile,
            msisdn_factor=msisdn.msisdn_factor,
            country="GB",
            sim_provider="NR",
            uploaded_by=msisdn.uploaded_by,
        )


class MsisdnCountDetails(CamelBaseModel):
    total_count: int
    national: int
    international: int

    class Config:
        schema_extra = {"example": FREE_MSISDN_COUNT_DETAILS}

    @classmethod
    def from_response_model(
        cls, msisdn_count_details: model.MsisdnCountDetails
    ) -> "MsisdnCountDetails":
        return cls(
            total_count=msisdn_count_details.total_count,
            national=msisdn_count_details.national,
            international=msisdn_count_details.international,
        )


class MsisdnResult(CamelBaseModel):
    total_msisdn: int
    error_msisdn: int
    error_results: list

    class Config:
        schema_extra = {"example": UPLOAD_MSISDN_RESULT}

    @classmethod
    def from_response_model(
        cls, total_msisdn: int, msisdn_details: model.MsisdnResult
    ) -> "MsisdnResult":
        return cls(
            total_msisdn=total_msisdn,
            error_msisdn=msisdn_details.error_msisdn,
            error_results=msisdn_details.error_result,
        )


class UpdateSimCardDetailsResult(CamelBaseModel):
    imsi: IMSI
    msisdn: MSISDN
    sim_profile: model.SimProfile

    class Config:
        schema_extra = {"example": SIM_MSISDN_RESULT}

    @classmethod
    def from_response_model(
        cls, sim_msisdn_details: model.UpdateSimCardDetailsResult
    ) -> "UpdateSimCardDetailsResult":
        return cls(
            imsi=sim_msisdn_details.imsi,
            msisdn=sim_msisdn_details.msisdn,
            sim_profile=sim_msisdn_details.sim_profile,
        )


class BulkSimCardUpdateResult(CamelBaseModel):
    total_details: int
    error_details: int
    error_results: list

    class Config:
        schema_extra = {"example": UPLOAD_MSISDN_RESULT}

    @classmethod
    def from_response_model(
        cls, sim_details: model.BulkSimCardUpdateResult
    ) -> "BulkSimCardUpdateResult":
        return cls(
            total_details=sim_details.total_details,
            error_details=sim_details.error_details,
            error_results=sim_details.error_results,
        )


class MSISDNMapping(CamelBaseModel):
    IMSI: CIMSI
    MSISDN: CMSISDN


class ResponseModel(CamelBaseModel):
    message: str
    request_id: str


class BulkUpdateRequest(CamelBaseModel):
    sim_profile: model.SimProfile
    msisdn_factor: model.MSISDNFactor
    msisdn_map: list[MSISDNMapping]

    class config:
        schema_extra = {"example": BULK_UPDATE_REQUEST}

    @root_validator(pre=True)
    def validate_combination(cls, values):
        sim_profile = values.get("simProfile")
        msisdn_factor = values.get("msisdnFactor")
        if (msisdn_factor == model.MSISDNFactor.INTERNATIONAL) and (
            sim_profile == model.SimProfile.VOICE_SMS_DATA
        ):
            raise ValueError(
                "sim_profile cannot be VOICE_SMS_DATA "
                "when msisdn_factor is INTERNATIONAL."
            )
        return values


class InvalidSimMsisdnCountDetails(CamelBaseModel):
    duplicate_iccids: int
    duplicate_imsis: int
    duplicate_msisdns: int
    duplicate_msisdn_pool_msisdns: int
    sim_card_msisdn_missing_msisdn_pool: int
    sim_card_allocation_id_mismatch_msisdn_pool: int
    create_range_count: int
    add_msisdn_pool_count: int
    update_sim_msisdn_count: int

    class Config:
        schema_extra = {"example": SIM_MSISDN_COUNT_DETAILS}

    @classmethod
    def from_response_model(
        cls, msisdn_count_details: model.InvalidSimMsisdnCountDetails
    ) -> "InvalidSimMsisdnCountDetails":
        return cls(
            duplicate_iccids=msisdn_count_details.duplicate_iccids,
            duplicate_imsis=msisdn_count_details.duplicate_imsis,
            duplicate_msisdns=msisdn_count_details.duplicate_msisdns,
            duplicate_msisdn_pool_msisdns=(
                msisdn_count_details.duplicate_msisdn_pool_msisdns
            ),
            sim_card_msisdn_missing_msisdn_pool=(
                msisdn_count_details.sim_card_msisdn_missing_msisdn_pool
            ),
            sim_card_allocation_id_mismatch_msisdn_pool=(
                msisdn_count_details.sim_card_allocation_id_mismatch_msisdn_pool
            ),
            create_range_count=msisdn_count_details.create_range_count,
            add_msisdn_pool_count=(msisdn_count_details.add_msisdn_pool_count),
            update_sim_msisdn_count=(msisdn_count_details.update_sim_msisdn_count),
        )


class UnallocateSimCardDetails(CamelBaseModel):
    message: str

    class Config:
        schema_extra = {"example": UNALLOCATED_SIM_CARDS}

    @classmethod
    def from_response_model(
        cls, unallocated_imsi: model.UnallocateSimCardDetails
    ) -> "UnallocateSimCardDetails":
        return cls(
            message=unallocated_imsi.message,
        )


class IMSIDeleteResponse(CamelBaseModel):
    message: str

    class Config:
        schema_extra = {"example": ISMI_DELETE_MESSAGE}

    @classmethod
    def from_response_model(
        cls, sim_details: model.IMSIDeleteResponse
    ) -> "IMSIDeleteResponse":
        return cls(
            message=sim_details.message,
        )


class SIMFlushResponse(CamelBaseModel):
    trid: str
    message: str
    imsi: IMSI
    msisdn = MSISDN


class SIMPODResponse(CamelBaseModel):
    trid: str
    message: str
    imsi: IMSI
    msisdn = MSISDN


class PushSMS(CamelBaseModel):
    imsi: IMSI
    message: Annotated[str, Field(max_length=250)]
    created_by: str

    class Config:
        schema_extra = {"example": SIM_SMS_REQUEST}


class SIMSendSMSResponse(CamelBaseModel):
    trid: str
    call_id: str
    imsi: IMSI
    msisdn = MSISDN


class SIMAPN(CamelBaseModel):
    imsi: IMSI
    apn_id: str
    created_by: str

    class Config:
        schema_extra = {"example": SIM_APN_REQUEST}


class SIMAPNResponse(CamelBaseModel):
    uuid: str
    message: str
    status: str


class DataSessionResponse(CamelBaseModel):
    id: str
    partner_id: int
    iccid: str
    imsi: str
    start_time: str
    last_updated: str
    end_time: str
    ip_address: str
    apn_name: str
    bytes_total: int
    bytes_mo: int
    bytes_mt: int
    bytes_limit: int
    bytes_limit_threshold: int
    bytes_limit_used: int
    mobile_country_code: str
    mobile_network_code: str
    lac: str
    cell_id: str

    @classmethod
    def from_model(cls, data_session: model.DataSession) -> "DataSessionResponse":
        return cls(
            id=data_session.id,
            partner_id=data_session.partner_id,
            iccid=data_session.iccid,
            imsi=data_session.imsi,
            start_time=data_session.start_time,
            last_updated=data_session.last_updated,
            end_time=data_session.end_time,
            ip_address=data_session.ip_address,
            apn_name=data_session.apn_name,
            bytes_total=data_session.bytes_total,
            bytes_mo=data_session.bytes_mo,
            bytes_mt=data_session.bytes_mt,
            bytes_limit=data_session.bytes_limit,
            bytes_limit_threshold=data_session.bytes_limit_threshold,
            bytes_limit_used=data_session.bytes_limit_used,
            mobile_country_code=data_session.mobile_country_code,
            mobile_network_code=data_session.mobile_network_code,
            lac=data_session.lac,
            cell_id=data_session.cellId,
        )


class LocationItemResponse(CamelBaseModel):
    id: str
    imsi: str
    first_location_update: str
    last_location_update: str
    msc_global_title: str | None
    vlr_global_title: str | None
    sgsn_global_title: str | None
    network_name: str
    country_name: str
    continent_name: str
    country_flag: str

    @classmethod
    def from_model(cls, location_item: model.LocationItem) -> "LocationItemResponse":
        return cls(
            id=location_item.id,
            imsi=location_item.imsi,
            first_location_update=location_item.first_location_update,
            last_location_update=location_item.last_location_update,
            msc_global_title=location_item.msc_global_title,
            vlr_global_title=location_item.vlr_global_title,
            sgsn_global_title=location_item.sgsn_global_title,
            network_name=location_item.network_name,
            country_name=location_item.country_name,
            continent_name=location_item.continent_name,
            country_flag=location_item.country_flag,
        )


class LocationMetaResponse(CamelBaseModel):
    current_page: int
    total_pages: int
    items_per_page: int
    total_items: int

    @classmethod
    def from_model(cls, location_meta: model.LocationMeta) -> "LocationMetaResponse":
        return cls(
            current_page=location_meta.current_page,
            total_pages=location_meta.total_pages,
            items_per_page=location_meta.items_per_page,
            total_items=location_meta.total_items,
        )


class LocationResponse(CamelBaseModel):
    meta: LocationMetaResponse
    items: list[LocationItemResponse]

    @classmethod
    def from_model(
        cls, location_response: model.LocationResponse
    ) -> "LocationResponse":
        return cls(
            meta=LocationMetaResponse.from_model(location_response.meta),
            items=[
                LocationItemResponse.from_model(item)
                for item in location_response.items
            ],
        )


class RangeDetails(CamelBaseModel):
    id: int
    title: str
    provider: str
    form_factor: str
    quantity: int
    imsi_first: IMSI
    imsi_last: IMSI
    remaining: int
    created_at: date
    created_by: str

    class Config:
        schema_extra = {"example": _NR_MICRO_RANGE}

    @classmethod
    def from_response_model(cls, range_details: model.Range) -> "RangeDetails":
        return cls(
            id=range_details.id,
            title=range_details.title,
            provider=range_details.provider,
            form_factor=range_details.form_factor,
            quantity=range_details.quantity,
            imsi_first=range_details.imsi_first,
            imsi_last=range_details.imsi_last,
            remaining=range_details.remaining,
            created_at=range_details.created_at,
            created_by=range_details.created_by,
        )
