CREATE_ORDER_REQUEST = {
    "customerDetails": {
        "customerName": "Kyivstar",
        "customerAccountName": "BTPushCDR",
        "customerAccountId": 1,
        "customerAccountLogoUrl": "https://example.com/logo.png",
        "customerId": "077ba04b-82f6-41a6-9d54-5a793a28fee6",
        "customerEmail": "<EMAIL>",
        "personPlacingOrder": "Test User",
        "customerContactNo": "**********",
        "customerReference": "Ref123",
    },
    "shippingDetails": {
        "contactName": "John <PERSON>",
        "addressLine1": "Address 1",
        "addressLine2": "Address 1",
        "city": "Bristol",
        "stateOrRegion": "South West England",
        "postalCode": "BS1 2AB",
        "country": "United Kingdom",
        "otherInformation": "",
    },
    "orderItems": [
        {"simType": "STANDARD", "quantity": 1500},
        {"simType": "MICRO", "quantity": 500},
    ],
    "orderBy": "John Doe",
}


CREATE_ORDER_RESPONSE = {
    "order_id": "ba6f3a9d-9d73-4620-935b-bbf9dd9921ab",
}

UPDATE_ORDER_REQUEST = {
    "order_id": "12345",
    "status": "Shipped",
    "message": "Order status updated successfully",
}

ORDER_1 = {
    "order_id": 1,
    "customer_account_name": "Acme Corp",
    "customer_account_logo_url": "https://cdn.acme/logo.png",
    "order_date": "2024-06-01T12:34:56",
    "person_placing_order": "Test User",
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "**********",
    "order_status_history": "Approved",
    "comments": "Order approved and ready for processing",
    "sim_details": [
        {"sim_type": "STANDARD", "quantity": 10},
        {"sim_type": "MICRO", "quantity": 5},
    ],
}

ORDER_2 = {
    "order_id": 2,
    "customer_account_name": "Acme Corp 2",
    "customer_account_logo_url": "https://cdn.acme/logo.png",
    "order_date": "2024-06-01T12:34:56",
    "person_placing_order": "Test User",
    "customer_name": "John Doe 2",
    "customer_email": "<EMAIL>",
    "customer_phone": "**********2",
    "order_status_history": "Approved",
    "comments": "Order approved and ready for processing",
    "sim_details": [
        {"sim_type": "STANDARD", "quantity": 10},
        {"sim_type": "MICRO", "quantity": 5},
    ],
}

ORDER_DETAILS_RESPONSE = {
    "id": 22,
    "orderBy": "John Doe",
    "orderId": "4ab7632a-f537-47e3-8bc1-792b8b87fe85",
    "orderDate": "2025-06-02T05:30:10.089292",
    "status": "REJECTED",
    "customerDetails": {
        "customer_id": "077ba04b-82f6-41a6-9d54-5a793a28fee6",
        "customer_email": "<EMAIL>",
        "customer_contact_no": "**********",
        "customer_name": "Kyivstar",
        "customer_account_name": "BTPushCDR",
        "person_placing_order": "Test User",
        "customer_reference": "Ref123",
        "uuid": "bf4d277c-f46f-4ca8-90fa-3260f8823aa7",
        "id": 1,
        "customer_account_id": 1,
        "customer_account_logo_url": "https://example.com/logo.png",
        "order_id": "4ab7632a-f537-47e3-8bc1-792b8b87fe85",
    },
    "shippingDetails": {
        "contact_name": "John Doe",
        "address_line1": "Address 1",
        "address_line2": "Address 1",
        "city": "Bristol",
        "state_or_region": "South West England",
        "postal_code": "BS1 2AB",
        "country": "United Kingdom",
        "uuid": "d3b3728a-7fa4-4465-9162-582fcb239e2a",
        "id": 1,
        "other_information": "",
        "order_id": "4ab7632a-f537-47e3-8bc1-792b8b87fe85",
    },
    "orderItems": [
        {
            "uuid": "162cfda2-6ba6-4aeb-b078-ed64db11444e",
            "id": 1,
            "order_id": "4ab7632a-f537-47e3-8bc1-792b8b87fe85",
            "sim_type": "STANDARD",
            "quantity": 1500,
        },
        {
            "uuid": "59382c9a-364d-43f8-aa1d-acf69073d791",
            "id": 2,
            "order_id": "4ab7632a-f537-47e3-8bc1-792b8b87fe85",
            "sim_type": "MICRO",
            "quantity": 500,
        },
    ],
    "comments": "No More sim available",
    "orderTracking": None,
}
